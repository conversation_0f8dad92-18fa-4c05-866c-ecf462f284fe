import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { sessionManager } from 'utils/sessionManager';
import { useSessionValidation as useSessionValidationHook } from 'hooks/useSessionValidation';

interface SessionContextType {
  isSessionValid: boolean;
  lastValidationTime: number | null;
  validateSession: () => void;
  isValidating: boolean;
  logout: () => Promise<void>;
  handleSingleSessionInvalidation: () => Promise<void>;
}

const SessionContext = createContext<SessionContextType | undefined>(undefined);

interface SessionProviderProps {
  children: ReactNode;
  /**
   * Whether to enable automatic session validation
   * Default: true
   */
  enableValidation?: boolean;
  
  /**
   * Validation interval in milliseconds
   * Default: 5 minutes
   */
  validationInterval?: number;
}

export const SessionValidationProvider: React.FC<SessionProviderProps> = ({
  children,
  enableValidation = true,
  validationInterval = 5 * 60 * 1000 // 5 minutes
}) => {
  const dispatch = useAppDispatch();
  const token = useAppSelector(state => state.persistedReducer.auth.access_token);
  
  // Initialize session manager with dispatch
  useEffect(() => {
    sessionManager.setDispatch(dispatch);
  }, [dispatch]);

  // Use session validation hook
  const {
    isSessionValid,
    lastValidationTime,
    validateSession,
    isValidating
  } = useSessionValidationHook({
    enabled: enableValidation && !!token,
    validationInterval,
    validateOnMount: true,
    validateOnFocus: true
  });

  // Session management functions
  const logout = async () => {
    await sessionManager.logout();
  };

  const handleSingleSessionInvalidation = async () => {
    await sessionManager.handleSingleSessionInvalidation();
  };

  const contextValue: SessionContextType = {
    isSessionValid,
    lastValidationTime,
    validateSession,
    isValidating,
    logout,
    handleSingleSessionInvalidation
  };

  return (
    <SessionContext.Provider value={contextValue}>
      {children}
    </SessionContext.Provider>
  );
};

export const useSessionValidation = (): SessionContextType => {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error('useSessionValidation must be used within a SessionValidationProvider');
  }
  return context;
};

// Export a hook that provides session management without requiring the context
export const useSessionManager = () => {
  const dispatch = useAppDispatch();
  
  useEffect(() => {
    sessionManager.setDispatch(dispatch);
  }, [dispatch]);

  return {
    logout: () => sessionManager.logout(),
    handleSingleSessionInvalidation: () => sessionManager.handleSingleSessionInvalidation(),
    validateSession: (token: string) => sessionManager.validateSession(token),
    invalidateSession: (options?: any) => sessionManager.invalidateSession(options)
  };
};
