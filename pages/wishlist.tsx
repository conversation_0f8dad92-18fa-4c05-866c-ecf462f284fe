import type { GetServerSideProps, NextPage } from 'next';
var cookie = require('cookie');

import { userAPI } from 'APIs';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { storeWishlist } from 'store/slices/productsSlice';

//import WishlistComponent from '@/modules/wishlist';

import dynamic from 'next/dynamic';
import { Suspense, useEffect } from 'react';
import Loading from '@/modules/common/loader';
import { toast } from 'react-toastify';

const WishlistComponent = dynamic(() => import('@/modules/wishlist'), {
  suspense: true,
});

const Wishlist: NextPage = () => {
  const dispatch = useAppDispatch();

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const getWishlistData = async () => {
    try {
      const res = await userAPI.getCustomerWishlist(token);
      if (res.items) {
        dispatch(storeWishlist(res));
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  useEffect(() => {
    getWishlistData();
  }, []);

  return (
    <Suspense fallback={<Loading />}>
      <div>
        <WishlistComponent />
      </div>
    </Suspense>
  );
};

export default Wishlist;

// export const getServerSideProps: GetServerSideProps = async (context) => {
//   const reqCookie = context.req.headers.cookie;
//   const token = reqCookie === undefined ? undefined : cookie.parse(reqCookie);
//   let wishlistedProducts;
//   if (reqCookie) {
//     wishlistedProducts = await userAPI.getCustomerWishlist(token.token);
//   }

//   return {
//     props: {
//       wishlistedProducts: wishlistedProducts || [],
//     },
//   };
// };
