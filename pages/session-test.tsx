import React, { useState } from 'react';
import { useAppSelector } from 'store/hooks';
import { useSessionManager } from 'contexts/SessionContext';
import { userAPI } from 'APIs';
import SessionStatus from 'components/SessionStatus';

const SessionTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const token = useAppSelector(state => state.persistedReducer.auth.access_token);
  const isSessionValid = useAppSelector(state => state.persistedReducer.auth.isSessionValid);
  const lastValidationTime = useAppSelector(state => state.persistedReducer.auth.lastValidationTime);
  
  const { 
    logout, 
    handleSingleSessionInvalidation, 
    validateSession,
    invalidateSession 
  } = useSessionManager();

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testApiCall = async () => {
    setIsLoading(true);
    addTestResult('Testing API call...');
    
    try {
      const response = await userAPI.getCustomer(token);
      if ('data' in response) {
        addTestResult('✅ API call successful');
      } else {
        addTestResult('❌ API call failed');
      }
    } catch (error) {
      addTestResult(`❌ API call error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testSessionValidation = async () => {
    setIsLoading(true);
    addTestResult('Testing session validation...');
    
    try {
      const isValid = await validateSession(token);
      addTestResult(`Session validation result: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
    } catch (error) {
      addTestResult(`❌ Session validation error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testLogout = async () => {
    addTestResult('Testing logout...');
    await logout();
  };

  const testSingleSessionInvalidation = async () => {
    addTestResult('Testing single session invalidation...');
    await handleSingleSessionInvalidation();
  };

  const testManualInvalidation = async () => {
    addTestResult('Testing manual session invalidation...');
    await invalidateSession({
      reason: 'session_expired',
      showToast: true,
      redirectTo: '/account/sign-in'
    });
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-gray-800">Session Management Test Page</h1>
        
        {/* Session Status */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current Session Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium text-gray-700">Token</h3>
              <p className={`text-sm ${token ? 'text-green-600' : 'text-red-600'}`}>
                {token ? '✓ Present' : '✗ Missing'}
              </p>
              {token && (
                <p className="text-xs text-gray-500 mt-1 break-all">
                  {token.substring(0, 20)}...
                </p>
              )}
            </div>
            
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium text-gray-700">Session Valid</h3>
              <p className={`text-sm ${
                isSessionValid === null ? 'text-yellow-600' : 
                isSessionValid ? 'text-green-600' : 'text-red-600'
              }`}>
                {isSessionValid === null ? '? Unknown' : 
                 isSessionValid ? '✓ Valid' : '✗ Invalid'}
              </p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium text-gray-700">Last Validation</h3>
              <p className="text-sm text-gray-600">
                {lastValidationTime ? 
                  new Date(lastValidationTime).toLocaleString() : 
                  'Never'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Test Actions */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <button
              onClick={testApiCall}
              disabled={isLoading || !token}
              className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-4 py-2 rounded transition-colors"
            >
              Test API Call
            </button>
            
            <button
              onClick={testSessionValidation}
              disabled={isLoading || !token}
              className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-4 py-2 rounded transition-colors"
            >
              Validate Session
            </button>
            
            <button
              onClick={testLogout}
              disabled={isLoading}
              className="bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white px-4 py-2 rounded transition-colors"
            >
              Test Logout
            </button>
            
            <button
              onClick={testSingleSessionInvalidation}
              disabled={isLoading}
              className="bg-orange-500 hover:bg-orange-600 disabled:bg-gray-400 text-white px-4 py-2 rounded transition-colors"
            >
              Single Session Invalidation
            </button>
            
            <button
              onClick={testManualInvalidation}
              disabled={isLoading}
              className="bg-purple-500 hover:bg-purple-600 disabled:bg-gray-400 text-white px-4 py-2 rounded transition-colors"
            >
              Manual Invalidation
            </button>
            
            <button
              onClick={clearResults}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
            >
              Clear Results
            </button>
          </div>
        </div>

        {/* Test Results */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">No test results yet. Click a test button above.</p>
            ) : (
              testResults.map((result, index) => (
                <div key={index} className="mb-1">
                  {result}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
      
      {/* Session Status Indicator */}
      <SessionStatus show={true} position="bottom-right" />
    </div>
  );
};

export default SessionTestPage;
