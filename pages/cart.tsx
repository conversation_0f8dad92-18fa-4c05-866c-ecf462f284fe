import type { GetServerSideProps, NextPage } from 'next';

// import CartComponent from '@/modules/cart/index';
import { ResponseItem } from 'models';
import { userAPI } from 'APIs';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { storeAllCartItems } from 'store/slices/cartSlice';
var cookie = require('cookie');
import Loading from '@/modules/common/loader';
import { Suspense, useEffect } from 'react';

import dynamic from 'next/dynamic';
import { toast } from 'react-toastify';

const CartComponent = dynamic(() => import('@/modules/cart/index'), {
  suspense: true,
});

interface Props {
  cartProducts: ResponseItem[];
}

const Cart: NextPage = () => {
  const dispatch = useAppDispatch();
  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const getCartData = async () => {
    try {
      const res = await userAPI.getCart(token);
      if ('data' in res) {
        dispatch(storeAllCartItems(res?.data?.items!));
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  useEffect(() => {
    getCartData();
  }, []);

  return (
    <>
      <Suspense fallback={<Loading />}>
        <div>
          <CartComponent />
        </div>
      </Suspense>
    </>
  );
};

export default Cart;

// export const getServerSideProps: GetServerSideProps = async (context) => {
//   const reqCookie = context.req.headers.cookie;
//   const token = reqCookie === undefined ? undefined : cookie.parse(reqCookie);
//   let response;
//   if (reqCookie) {
//     response = await userAPI.getCart(token.token);
//   }
//   return {
//     props: {
//       cartProducts: response?.data?.items!,
//     },
//   };
// };
