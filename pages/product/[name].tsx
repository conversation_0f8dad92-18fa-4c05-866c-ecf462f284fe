import type { GetServerSideProps, NextPage } from 'next';

import { userAPI } from 'APIs';
import { Product } from 'models';

//import ProductDetailsComponent from '@/modules/productPage';
import { useRouter } from 'next/router';
import { Suspense, useEffect, useState } from 'react';

import dynamic from 'next/dynamic';
import Loading from '@/modules/common/loader';

const ProductDetailsComponent = dynamic(() => import('@/modules/productPage'), {
  suspense: true,
});

interface SingleProduct {
  product: Product;
}

const ProductDetails: NextPage<SingleProduct> = ({ product }) => {
  const router = useRouter();
  const [ready, setReady] = useState(false);

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
    }
  }, [router.isReady]);

  if (!ready) return null;
  return (
    <>
      <Suspense fallback={<Loading />}>
        <div>
          <ProductDetailsComponent product={product} />
        </div>
      </Suspense>
    </>
  );
};
export const getServerSideProps: GetServerSideProps = async (context) => {
  const pUniqueName = context?.params?.name! as string;
  const res = await userAPI.getPublicProductByUniqueName(pUniqueName);
  return {
    props: {
      product: res,
    },
  };
};

export default ProductDetails;
