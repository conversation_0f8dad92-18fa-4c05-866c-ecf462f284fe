import type { NextPage } from 'next';

//import BlogDetails from '@/modules/blog/components/singleBlog';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import dynamic from 'next/dynamic';
import { userAPI } from 'APIs';
import { Blog } from 'models';
import { toast } from 'react-toastify';
import { useAppSelector } from 'store/hooks';
import Loading from '@/modules/common/loader';

const BlogDetails = dynamic(
  () => import('@/modules/blog/components/singleBlog')
);

const SingleBlogDetailsPage: NextPage = () => {
  const router = useRouter();
  const blogId = router.query.id! as string;
  const [ready, setReady] = useState(false);
  const [blog, setBlog] = useState();
  const [loader, setLoader] = useState(false);
  const [recentList, setRecentList] = useState<Blog[]>();
  const [currentMonthsBlogs, setCurrentMonthsBlogs] = useState<Blog[]>();

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const getBlogs = async () => {
    setLoader(true);
    try {
      const res = await userAPI.getWpBlogs(0, 3);
      if ('data' in res) {
        setRecentList(res.data);
      }
      const currentMonth = await userAPI.getWpBlogs(0, 10);
      if ('data' in currentMonth!) {
        setCurrentMonthsBlogs(currentMonth?.data!);
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
    setLoader(false);
    setReady(true);
  };

  const getBlogBySlug = async () => {
    try {
      const res = await userAPI.getWpSingleBlog(blogId);
      setBlog(res![0]);
    } catch (error) {}
    setReady(true);
  };

  useEffect(() => {
    if (router.isReady) {
      getBlogBySlug();
      getBlogs();
    }
  }, [router.isReady]);

  if (!ready) return null;

  //if (loader) return <Loading />;
  return (
    <>
      {blog && (
        <BlogDetails
          blog={blog}
          recentList={recentList!}
          currentMonthsList={currentMonthsBlogs!}
        />
      )}
    </>
  );
};

export default SingleBlogDetailsPage;
