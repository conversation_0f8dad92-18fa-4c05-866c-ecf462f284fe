import useTranslation from 'next-translate/useTranslation';
import dynamic from 'next/dynamic';

const AddPostForm = dynamic(() => import('@/modules/post/addPost'));
function AddPost() {
  const { t } = useTranslation();
  return (
    <div>
      <div className="flex flex-wrap justify-center">
        <div
          className="my-20 mx-3 flex flex-col py-7"
          style={{
            width: ' 35rem ',
            height: 'auto',
            background: '#f3f3f3',
          }}
        >
          <h2 className="mx-3 text-center text-3xl text-gray-800">Add post</h2>
          <div className="m-5 my-3 sm:m-5 md:mx-10 lg:mx-10 xl:mx-10">
            <AddPostForm />
          </div>
        </div>
      </div>
    </div>
  );
}

export default AddPost;
