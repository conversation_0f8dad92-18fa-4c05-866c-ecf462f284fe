import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const { status } = req.query;

    // Get your app's base URL
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:4003';

    // Redirect based on status
    switch (status?.toString().toLowerCase()) {
      case 'success':
        return res.redirect(`${baseUrl}/subscription/success`);

      case 'failed':
      case 'failure':
        return res.redirect(`${baseUrl}/subscription/failure`);

      case 'canceled':
      case 'cancelled':
      case 'cancel':
        return res.redirect(`${baseUrl}/subscription/cancel`);

      default:
        return res.redirect(`${baseUrl}/subscription/failure`);
    }
  } catch (error) {
    console.error('bKash webhook error:', error);
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:4003';
    return res.redirect(`${baseUrl}/subscription/failure?error=webhook_error`);
  }
}
