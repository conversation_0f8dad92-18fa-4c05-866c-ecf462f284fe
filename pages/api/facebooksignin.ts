import axios from 'axios';
import { config } from 'config';

var cookie = require('cookie');
var escapeHtml = require('escape-html');
var http = require('http');
var url = require('url');

export default async function handler(req: any, res: any) {
  try {
    const accessToken = req.body;
    console.log('🔑 Facebook signin API called with token:', accessToken ? 'Present' : 'Missing');

    const token = await fetch(
      `${config?.restPrefix}/user-auth/facebook/sign-in`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accessToken,
        }),
      }
    );

    const data = await token.json();
    console.log('🔄 Backend response:', data);
    console.log('🔄 Backend status:', token.status);

    // Handle successful authentication (backend auto-creates users)
    // Accept both 200 (OK) and 201 (Created) as success
    if ((token.status === 200 || token.status === 201) && data.data && data.data.token) {
      res.setHeader(
        'Set-Cookie',
        cookie.serialize('token', data.data.token, {
          httpOnly: true,
          maxAge: 60 * 60 * 24 * 7,
          sameSite: 'strict',
          path: '/',
        })
      );
      console.log('✅ Cookie set successfully');
      res.status(200).json(data);
      return;
    }

    // Handle authentication errors properly
    if (token.status !== 200 && token.status !== 201) {
      console.log('❌ Backend authentication failed with status:', token.status);
      res.status(token.status).json({
        error: 'AUTHENTICATION_FAILED',
        message: 'Authentication failed. Please try again.',
        originalError: data
      });
      return;
    }

    // Fallback for unexpected responses
    console.log('❌ Unexpected response format');
    res.status(400).json({
      error: 'INVALID_RESPONSE',
      message: 'Invalid response from authentication service.'
    });
    axios.defaults.headers.common['Authorization'] = `${token}`;
  } catch (error) {
    console.error('💥 Facebook signin API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
