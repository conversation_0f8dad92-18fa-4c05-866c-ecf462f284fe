import { getToken } from 'next-auth/jwt';

const secret = process.env.NEXTAUTH_SECRET;
let accessToken;

export default async (req: any, res: any) => {
  try {
    console.log('🎫 TokenHandler called');
    const token = await getToken({ req, secret });
    console.log('🎫 NextAuth token:', token);
    accessToken = token?.access_token;
    console.log('🎫 Access token extracted:', accessToken ? 'Found' : 'Not found');

    res.status(200).send({
      token: accessToken
    });
  } catch (error) {
    console.error('💥 TokenHandler error:', error);
    res.status(500).json({ error: 'Failed to get token' });
  }
};
