import '@/styles/globals.css';
//import '@/styles/timer.css';
import Script from 'next/script';

import Axios from 'axios';
import dynamic from 'next/dynamic';
import type { AppProps } from 'next/app';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';

import { config } from 'config';
import { persistor, store } from 'store/store';
import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from 'next-themes';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { ToastContainer } from 'react-toastify';
import GlobalAuthProvider from '@/modules/auth/globalAuthProvider';
// import BackToTopButton from './BackToTopButton';

Axios.defaults.baseURL = config.restPrefix;
Axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response && error.response.data) {
      if (error.response.status === 401) {
        if (!window.location.pathname.includes('/product'))
          window.location.href = '/account/sign-in?clear=true';
      }
      return Promise.reject(error.response.data);
    }
    return Promise.reject(error.message);
  }
);

const Layout = dynamic(() => import('@/modules/common/layout'));
const Viewport = dynamic(() => import('@/modules/common/layout/viewport'));
const BackToTopButton = dynamic(
  () => import('@/modules/common/layout/backToTopButton')
);

function MyApp({ Component, pageProps: { session, ...pageProps } }: AppProps) {
  const { t } = useTranslation();
  const [isMounted, setIsMounted] = useState(false);
  const router = useRouter();

  const withoutLayoutWrapList = [
    '/',
    '/return-policy',
    '/privacy-policy',
    '/terms-of-service',
    '/disclaimer',
    '/about',
    '/faq',
    '/blogs',
    '/contact',
    '/blogs/[id]',
    '/clubs/disclaimer',
    '/subscription-policy',
    '/terms-of-service/ios',
    '/terms-of-service/android',
    // '/account/sign-in',
    // '/account/sign-up',
  ];
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // console.log(process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS);

  return (
    <div>
      {withoutLayoutWrapList.includes(router.pathname) ? (
        <>
          <SessionProvider session={session}>
            <ThemeProvider enableSystem={false} attribute="class">
              <Provider store={store}>
                <PersistGate loading={null} persistor={persistor}>
                  <GlobalAuthProvider>
                    <Viewport />
                    {isMounted && <Component {...pageProps} />}
                    {/* <BackToTopButton /> */}
                    {/* {router.pathname !== '/' && (
                      <footer className="hidden w-full bg-gray-900 py-4 text-center text-sm font-normal text-white md:px-4 md:text-left lg:flex">
                        <div className="container mx-auto flex justify-center">
                          <p className=""> {t('common:copyright')}</p>
                        </div>
                      </footer>
                    )} */}
                    <ToastContainer
                      enableMultiContainer
                      theme="colored"
                      containerId={'bottom-right'}
                      position="bottom-right"
                    />
                  </GlobalAuthProvider>
                </PersistGate>
              </Provider>
            </ThemeProvider>
          </SessionProvider>
        </>
      ) : (
        <SessionProvider session={session}>
          <ThemeProvider enableSystem={false} attribute="class">
            <Provider store={store}>
              <PersistGate loading={null} persistor={persistor}>
                <GlobalAuthProvider>
                  <Layout>
                    {isMounted && <Component {...pageProps} />}
                    {/* <BackToTopButton /> */}
                  </Layout>
                </GlobalAuthProvider>
              </PersistGate>
            </Provider>
          </ThemeProvider>
        </SessionProvider>
      )}
    </div>
  );
}

export default MyApp;
