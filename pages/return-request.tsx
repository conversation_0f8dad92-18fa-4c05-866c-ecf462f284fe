// import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
// import ReturnComponent from '@/modules/returnRequest';

import dynamic from 'next/dynamic';

const Breadcrumb = dynamic(
  () => import('@/modules/common/breadcrumbs/breadcrumb')
);

const ReturnComponent = dynamic(() => import('@/modules/returnRequest'));

const ReturnRequestPage = () => {
  return (
    <>
      <Breadcrumb
        title="Return Request"
        pathArray={['Home', 'Return Request']}
        linkArray={['/', '/return-request']}
      />
      <div className="container mx-auto p-4">
        <ReturnComponent />
      </div>
    </>
  );
};

export default ReturnRequestPage;
