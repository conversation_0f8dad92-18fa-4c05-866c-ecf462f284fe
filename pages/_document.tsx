import { Head, Html, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="en" className="h-100">
      <Head>
        <link rel="icon" href="/fitsomnia-icon.ico" sizes="any" />

        <link
          href="https://fonts.googleapis.com/css2?family=Outfit:wght@200;300&display=swap"
          rel="stylesheet"
        />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="true"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;500;600;700&display=swap"
          rel="stylesheet"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </Head>

      <body className="d-flex flex-column h-100 dark:bg-dark_bg dark:text-dark_text">
        <Main />
        <NextScript />
        {/* <script
          src="https://cdn.jsdelivr.net/gh/brainstationrandd/chatbot-widget@main/chatbot-23-v1.js"
          async
        ></script> */}

        <script
          //@ts-ignore
          strategy="afterInteractive"
          async
          src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS}`}
        ></script>

        <script
          id="google-analytics"
          //@ts-ignore
          strategy="afterInteractive"
        >
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', '${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS}');
        `}
        </script>
      </body>
    </Html>
  );
}
