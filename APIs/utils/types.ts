import {
  AddProductQuestionSuccessResponse,
  AddToCartRequest,
  AddToWishlistRequest,
  AddressData,
  AddressValidationSuccessResponse,
  CancelOrderSuccessResponse,
  CreateReviewRequest,
  CreateReviewResponse,
  // GetCustomerQuery,
  // GetCustomerResponse,
  // ForgotPasswordRequest,
  // ForgotPasswordResponse,
  CreateUserRequest,
  DeleteReviewListResponse,
  GetCartSuccessResponse,
  GetUserAllProductsSuccessResponse,
  GetUserInformationSuccessResponse,
  GetUserWishlistSuccessResponse,
  IPaymentMethodListSuccessRes,
  IProductSearchResponse,
  IReOrderQuery,
  IStripePaymentResponse,
  OrderByUserIdResponse,
  OrderResponseData,
  ProductQuestionsWithAnswerForUserSuccessResponse,
  ReviewListResponse,
  SendOtpSuccessResponse,
  SubscriberResponse,
  SuccessResponse,
  UpdateCartItemRequest,
  UpdateUserRequestBody,
  UpdateUserSuccessResponse,
  UserAddress,
  UserForgotPasswordRequest,
  UserForgotPasswordSuccessResponse,
  UserSignInRequest,
  VerifyOtpRequest,
  VerifyOtpSuccessResponse,
  Wishlist,
} from 'models';
import {
  FileUploadRequestBody,
  PublicUploadFileSuccessResponse,
  UploadFileSuccessResponse,
} from 'models/media';
import { NextRouter } from 'next/router';

export interface accordionBody {
  id: string;
  title: string;
  body: string;
}

export interface storiesBody {
  id: string;
  image: string;
  title: string;
  description: string;
}

export interface CarouselBody {
  id: string;
  title: string;
  body: string;
  image: string;
}

export interface apiFunction {
  signIn: (data: UserSignInRequest) => Promise<SuccessResponse | undefined>;
  // getSignedInUser: (
  //   isEmail: boolean,
  //   data: GetCustomerQuery
  // ) => Promise<GetCustomerResponse | undefined>;
  sendOTP: (data: string) => Promise<SuccessResponse | undefined>;
  signUp: (data: CreateUserRequest) => Promise<SuccessResponse | undefined>;
  getPublicProducts: () => Promise<
    GetUserAllProductsSuccessResponse | undefined
  >;
  getFeaturedProducts: () => Promise<SuccessResponse | undefined>;
  getPublicProductsById: (
    productId: string
  ) => Promise<SuccessResponse | undefined>;
  getCategoryList: () => Promise<SuccessResponse | undefined>;
  getPublicProductByCategoryId: (
    categoryId: string,
    orderBy: string,
    minPrice: number,
    maxPrice: number,
    brands: string,
    skip: number,
    limit: number
  ) => Promise<SuccessResponse | undefined>;
  checkout: (
    data: any,
    router: NextRouter
  ) => Promise<OrderResponseData | undefined>;
  getOrderProducts: (
    token: string
  ) => Promise<OrderByUserIdResponse | undefined>;
  getOrderProduct: (OrderId: string) => Promise<OrderResponseData | undefined>;
  addToWishList: (
    data: AddToWishlistRequest
  ) => Promise<SuccessResponse | undefined>;
  getCustomerWishlist: (token: string) => Promise<GetUserWishlistSuccessResponse | any>;
  deleteWishlistItem: (data: string) => Promise<SuccessResponse | undefined>;
  deleteFullWishlist: () => Promise<SuccessResponse | undefined>;
  addToCompare: (productId: string) => Promise<SuccessResponse | undefined>;
  deleteFromCompare: (
    productId: string
  ) => Promise<SuccessResponse | undefined>;
  getCustomerProfile: (
    token: string
  ) => Promise<GetUserInformationSuccessResponse | any>;
  deleteUserAddress: (
    addressId: string
  ) => Promise<SuccessResponse | undefined>;
  updateUserAddress: (
    addressId: string,
    data: UserAddress
  ) => Promise<SuccessResponse | undefined>;

  addCustomerNewAddress: (
    UserAddress: UserAddress
  ) => Promise<SuccessResponse | undefined>;
  getCustomer: (
    token: string
  ) => Promise<GetUserInformationSuccessResponse | undefined>;
  updateCustomer: (
    data: UpdateUserRequestBody
  ) => Promise<UpdateUserSuccessResponse | undefined>;

  getCart: (data: string) => Promise<GetCartSuccessResponse | undefined>;
  addToCart: (data: AddToCartRequest) => Promise<SuccessResponse | undefined>;
  deleteAllCartItem: () => Promise<SuccessResponse | undefined>;
  deleteSingleCartItem: (
    productId: string
  ) => Promise<SuccessResponse | undefined>;
  updateCartItem: (
    cartItem: UpdateCartItemRequest
  ) => Promise<SuccessResponse | undefined>;
  forgetPasswordSendOtp: (
    data: string
  ) => Promise<SendOtpSuccessResponse | undefined>;
  forgetPasswordVerifyOtp: (
    data: VerifyOtpRequest
  ) => Promise<VerifyOtpSuccessResponse | undefined>;
  resetPassword: (
    data: UserForgotPasswordRequest
  ) => Promise<UserForgotPasswordSuccessResponse | undefined>;
  getBrands(): Promise<SuccessResponse | undefined>;
  getPublicProductByUniqueName(
    productUniqueName: string
  ): Promise<SuccessResponse | undefined>;
  getCategoryDetailsById: (
    categoryId: string
  ) => Promise<SuccessResponse | undefined>;
  getCategoryDetailsBySlug: (
    categorySlug: string
  ) => Promise<SuccessResponse | undefined>;
  searchProducts(
    searchText: string,
    pageNumber: number,
    limit: number
  ): Promise<IProductSearchResponse | undefined>;
  getCompare: () => Promise<SuccessResponse | undefined>;
  reorder: (
    reorderParams: IReOrderQuery
  ) => Promise<SuccessResponse | undefined>;
  askQuestionAboutProduct(
    productId: string,
    question: string
  ): Promise<AddProductQuestionSuccessResponse>;
  getQuestionsAboutProduct(
    productId: string,
    offset: number,
    limit: number
  ): Promise<ProductQuestionsWithAnswerForUserSuccessResponse>;
  uploadPublicMedia(
    file: any,
    featureName: string
  ): Promise<PublicUploadFileSuccessResponse>;
  createReview(data: CreateReviewRequest): Promise<CreateReviewResponse>;
  getReview(
    productId: string,
    skip?: number,
    limit?: number
  ): Promise<ReviewListResponse>;
  uploadPrivateMedia(
    data: FileUploadRequestBody
  ): Promise<UploadFileSuccessResponse>;
  getUserPoints(): Promise<any>;
  stripeRepay(orderId: string): Promise<IStripePaymentResponse>;
  getPaymentMethods(): Promise<IPaymentMethodListSuccessRes>;
  createCheckoutSummary(data: any): Promise<any>;
  deleteReview(reviewId: string): Promise<DeleteReviewListResponse>;
  updateReview(
    orderId: string,
    data: CreateReviewRequest
  ): Promise<CreateReviewResponse>;
  joinWaitList(email: string): Promise<SubscriberResponse>;
  validateAddress(data: AddressData): Promise<AddressValidationSuccessResponse>;
  cancelOrder(id: string): Promise<CancelOrderSuccessResponse | any> 
  getWpBlogs(page: number, limit: number): Promise<any>;
  getWpSingleBlog(slug: string): Promise<any>;
}
