import {
  addCustomerNewAddressRest,
  addToCartRest,
  addToCompareRest,
  addToWishlistRest,
  askQuestionAboutProductRest,
  cancelOrderRest,
  checkoutRest,
  createCheckoutSummaryRest,
  createPostRest,
  createReviewRest,
  deleteAllCartItemRest,
  deleteFromCompareRest,
  deleteFullWishlistRest,
  deleteReviewRest,
  deleteSingleCartItemRest,
  deleteUserAddressRest,
  deleteWishlistItemRest,
  forgetPasswordSendOtpRest,
  forgetPasswordVerifyOtpRest,
  getBlogByIdRest,
  getBlogsRest,
  getBrandsRest,
  getCartRest,
  getCategoryDetailsByIdRest,
  getCategoryDetailsBySlugRest,
  getCategoryListRest,
  getCompareRest,
  getCustomerProfileRest,
  getCustomerRest,
  getCustomerWishlistRest,
  getFeaturedProductsRest,
  getFollowerStoriesRest,
  getMyStoryRest,
  getNewsFeedRest,
  getOrderProductRest,
  getOrderProductsRest,
  getPaymentMethodsRest,
  getPlansRest,
  getPublicProductByCategoryIDRest,
  getPublicProductByIdRest,
  getPublicProductByUniqueNameRest,
  getPublicProductsRest,
  getQuestionsAboutProductRest,
  getReviewRest,
  getUserPointsRest,
  getWpBlogsRest,
  getWpSingleBlogRest,
  joinWaitListRest,
  reorderRest,
  resetPasswordRest,
  searchProductsRest,
  sendMultiReactionRest,
  sendOTPRest,
  signinRest,
  signUpRest,
  stripeRepayRest,
  subscriptionCheckoutRest,
  updateCartItemRest,
  updateCustomerRest,
  updateReviewRest,
  updateUserAddressRest,
  uploadPrivateMediaRest,
  uploadPublicMediaRest,
  validateAddressRest,
} from './restApi';

const restApi = {
  signUp: signUpRest,
  sendOTP: sendOTPRest,
  signIn: signinRest,
  // getSignedInUser: getSignedInUserRest,
  getPublicProducts: getPublicProductsRest,
  getPublicProductsById: getPublicProductByIdRest,
  getPublicProductByCategoryId: getPublicProductByCategoryIDRest,
  getFeaturedProducts: getFeaturedProductsRest,
  getCategoryList: getCategoryListRest,
  checkout: checkoutRest,
  getOrderProducts: getOrderProductsRest,
  getOrderProduct: getOrderProductRest,
  addToWishList: addToWishlistRest,
  getCustomerWishlist: getCustomerWishlistRest,
  deleteWishlistItem: deleteWishlistItemRest,
  deleteFullWishlist: deleteFullWishlistRest,
  addToCompare: addToCompareRest,
  deleteFromCompare: deleteFromCompareRest,
  getCustomerProfile: getCustomerProfileRest,
  deleteUserAddress: deleteUserAddressRest,
  updateUserAddress: updateUserAddressRest,
  addCustomerNewAddress: addCustomerNewAddressRest,
  getCustomer: getCustomerRest,
  updateCustomer: updateCustomerRest,
  getCart: getCartRest,
  addToCart: addToCartRest,
  deleteAllCartItem: deleteAllCartItemRest,
  deleteSingleCartItem: deleteSingleCartItemRest,
  updateCartItem: updateCartItemRest,
  forgetPasswordSendOtp: forgetPasswordSendOtpRest,
  forgetPasswordVerifyOtp: forgetPasswordVerifyOtpRest,
  resetPassword: resetPasswordRest,
  getBrands: getBrandsRest,
  getCategoryDetailsById: getCategoryDetailsByIdRest,
  getCategoryDetailsBySlug: getCategoryDetailsBySlugRest,
  getPublicProductByUniqueName: getPublicProductByUniqueNameRest,
  searchProducts: searchProductsRest,
  getCompare: getCompareRest,
  reorder: reorderRest,
  askQuestionAboutProduct: askQuestionAboutProductRest,
  getQuestionsAboutProduct: getQuestionsAboutProductRest,
  uploadPublicMedia: uploadPublicMediaRest,
  createReview: createReviewRest,
  getReview: getReviewRest,
  uploadPrivateMedia: uploadPrivateMediaRest,
  getUserPoints: getUserPointsRest,
  stripeRepay: stripeRepayRest,
  getPaymentMethods: getPaymentMethodsRest,
  createCheckoutSummary: createCheckoutSummaryRest,
  deleteReview: deleteReviewRest,
  updateReview: updateReviewRest,
  joinWaitList: joinWaitListRest,
  validateAddress: validateAddressRest,
  getBlog: getBlogsRest,
  getBlogById: getBlogByIdRest,
  cancelOrder: cancelOrderRest,
  getWpBlogs: getWpBlogsRest,
  getWpSingleBlog: getWpSingleBlogRest,
  getNewsFeed: getNewsFeedRest,
  createPost: createPostRest,
  getMyStory: getMyStoryRest,
  getFollowerStories: getFollowerStoriesRest,
  sendMultiReaction: sendMultiReactionRest,
  getPlans: getPlansRest,
  subscriptionCheckout: subscriptionCheckoutRest,
};

export const userAPI = restApi;
