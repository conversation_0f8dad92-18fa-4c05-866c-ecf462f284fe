import React from 'react';
import { Formik, Field, Form, ErrorMessage } from 'formik';
import Link from 'next/link';
import useTranslation from 'next-translate/useTranslation';
import { usernameSchema } from '@/modules/account/schemas/forgotPassword.schema';

interface Props {
  handleUsernameFormSubmit: Function;
}

export const UsernameForm: React.FC<Props> = ({ handleUsernameFormSubmit }) => {
  const { t } = useTranslation();

  return (
    <>
      <div data-testid="hygen">
        <Formik
          initialValues={{
            username: '',
          }}
          onSubmit={(values, actions) => {
            handleUsernameFormSubmit(values.username);
            actions.setSubmitting(false);
          }}
          validationSchema={usernameSchema}
        >
          {(formikprops) => {
            return (
              <Form onSubmit={formikprops.handleSubmit}>
                <>
                  <div className="mb-4">
                    <Field
                      type="text"
                      className="w-full p-2 placeholder-gray-600 outline-0"
                      id="username"
                      name="username"
                      placeholder={t('forgot-password:username')}
                    />
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="username" />
                    </div>
                  </div>
                </>

                <div className="flex flex-wrap justify-end sm:justify-end md:justify-between lg:justify-between xl:justify-between">
                  <button
                    type="submit"
                    className={`my-2 w-full rounded bg-primary py-2 capitalize text-white transition-all duration-500 ease-in-out hover:bg-black dark:bg-dark_primary  sm:w-full md:w-1/4 lg:w-1/4 xl:w-1/4 `}
                  >
                    {t('common:submit')}
                  </button>

                  <div
                    id="cancelDiv"
                    className="text-decoration-none font-weight-light my-0 text-gray-600 hover:text-gray-500 sm:my-0 md:my-3 lg:my-3 xl:my-3"
                  >
                    <Link prefetch={false} href="/account/sign-in">
                      {t('common:cancel')}
                    </Link>
                  </div>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
    </>
  );
};
