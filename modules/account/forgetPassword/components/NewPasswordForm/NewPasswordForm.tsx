import React from 'react';
import { Formik, Field, Form, ErrorMessage } from 'formik';
import Link from 'next/link';
import useTranslation from 'next-translate/useTranslation';
import { useAppSelector } from 'store/hooks/index';
import { passwordSchema } from '@/modules/account/schemas/forgotPassword.schema';

interface Props {
  handleNewPasswordFormSubmit: Function;
}

export const NewPasswordForm: React.FC<Props> = ({
  handleNewPasswordFormSubmit,
}) => {
  const { t } = useTranslation();

  const otpDetail = useAppSelector(
    (state) => state.persistedReducer.forgetPassword
  );

  return (
    <>
      <div data-testid="hygen">
        <Formik
          initialValues={{
            newPassword: '',
            confirmPassword: '',
          }}
          onSubmit={(values, actions) => {
            let regex = new RegExp('[a-z0-9]+@[a-z]+.[a-z]{2,3}');
            const isEmail = regex.test(otpDetail.username);
            let data;
            isEmail
              ? (data = {
                  email: otpDetail.username,
                  password: values.newPassword,
                })
              : (data = {
                  phone: otpDetail.username,
                  password: values.newPassword,
                });
            handleNewPasswordFormSubmit(data);
            actions.setSubmitting(false);
          }}
          validationSchema={passwordSchema}
        >
          {(formikprops) => {
            return (
              <Form onSubmit={formikprops.handleSubmit}>
                <>
                  <div className="mb-4">
                    <Field
                      type="password"
                      className="w-full p-2 placeholder-gray-600 outline-0"
                      id="newPassword"
                      name="newPassword"
                      placeholder={t('forgot-password:new_password')}
                    />
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="newPassword" />
                    </div>
                  </div>
                </>

                <>
                  <div className="mb-4">
                    <Field
                      type="password"
                      className="w-full p-2 placeholder-gray-600 outline-0"
                      id="confirmPassword"
                      name="confirmPassword"
                      placeholder={t('forgot-password:retype_new_password')}
                    />
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="confirmPassword" />
                    </div>
                  </div>
                </>

                <div className="flex flex-wrap justify-end sm:justify-end md:justify-between lg:justify-between xl:justify-between">
                  <button
                    type="submit"
                    className={`my-2 w-full rounded bg-primary py-2 capitalize text-white transition-all duration-500 ease-in-out hover:bg-black dark:bg-dark_primary  sm:w-full md:w-1/4 lg:w-1/4 xl:w-1/4 `}
                  >
                    {t('common:submit')}
                  </button>

                  <div
                    id="cancelDiv"
                    className="text-decoration-none font-weight-light my-0 text-gray-600 hover:text-gray-500 sm:my-0 md:my-3 lg:my-3 xl:my-3"
                  >
                    <Link prefetch={false} href="/account/sign-in">
                      {t('common:cancel')}
                    </Link>
                  </div>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
    </>
  );
};
