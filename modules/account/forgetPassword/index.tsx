import { userAPI } from 'APIs';
import { UserForgotPasswordRequest, VerifyOtpRequest } from 'models';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from 'store/hooks/index';
import { storeOtp, storeUsername } from 'store/slices/forgetPasswordSlice';

import { NewPasswordForm } from '@/modules/account/forgetPassword/components/NewPasswordForm';
import { OtpForm } from '@/modules/account/forgetPassword/components/OtpForm';
import { UsernameForm } from '@/modules/account/forgetPassword/components/UsernameForm';
import WithoutAuth from '@/modules/auth/withoutAuth';
import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import { handleLogout } from 'helper/handleLogout';
import { toast } from 'react-toastify';
import Loading from '@/modules/common/loader';

const ForgotPassword: React.FC = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { t } = useTranslation();

  const [submitButtonState, setSubmitButtonState] =
    useState<string>('username');

  const [loader, setLoader] = useState(false);

  const handleUsernameFormSubmit = async (username: string) => {
    setLoader(true);
    try {
      const res = await userAPI.forgetPasswordSendOtp(username);
      if ('data' in res) {
        const resStringArray = res?.data?.message?.split(' ');
        const otp = parseInt(resStringArray![resStringArray?.length! - 1]);
        dispatch(storeUsername(username));
        dispatch(storeOtp(otp));
        setSubmitButtonState('otp');
        toast.success('OTP sent', {
          containerId: 'bottom-right',
        });
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
    setLoader(false);
  };

  const handleOtpFormSubmit = async (data: VerifyOtpRequest) => {
    setLoader(true);
    try {
      const res = await userAPI.forgetPasswordVerifyOtp(data);
      if ('data' in res) {
        setSubmitButtonState('newPassword');
      } else {
        toast.success(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
    setLoader(false);
  };

  const handleNewPasswordFormSubmit = async (
    data: UserForgotPasswordRequest
  ) => {
    setLoader(true);
    try {
      const res = await userAPI.resetPassword(data);
      if ('data' in res) {
        router.push('/account/sign-in');
        toast.success('Password updated successfully', {
          containerId: 'bottom-right',
        });
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
        setSubmitButtonState('username');
      }
    } catch (error) {}
    setLoader(false);
  };

  const providerName = useAppSelector(
    (state) => state?.persistedReducer?.provider?.provider
  );

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  // Removed automatic logout logic that was causing issues
  // useEffect(() => {
  //   if (token) {
  //     handleLogout(localStorage, dispatch, providerName, router);
  //   }
  // }, []);

  if (loader) return <Loading />;

  return (
    <>
      {/* <Breadcrumb
        title={t('common:account')}
        pathArray={[
          `${t('common:home')}`,
          `${t('forgot-password:forgot_password')}`,
        ]}
        linkArray={['/market', '/account/forgot-password']}
      /> */}
      <div className="flex flex-wrap justify-center">
        <div
          className="my-20 mx-3 flex flex-col py-7"
          style={{ width: ' 35rem ', height: 'auto', background: '#f3f3f3' }}
        >
          <h2 className="mx-3 text-center text-3xl text-gray-800">
            {t('forgot-password:reset_password')}
          </h2>
          <p className="mx-5 mt-2 mb-6 text-center text-gray-500">
            {submitButtonState === 'username' && t('forgot-password:send_code')}

            {submitButtonState === 'otp' && t('forgot-password:code_sent')}

            {submitButtonState === 'newPassword' &&
              t('forgot-password:submit_new_password')}
          </p>
          <div className="m-5 my-3 sm:m-5 md:mx-10 lg:mx-10 xl:mx-10">
            {submitButtonState === 'username' && (
              <UsernameForm
                handleUsernameFormSubmit={handleUsernameFormSubmit}
              />
            )}

            {submitButtonState === 'otp' && (
              <OtpForm
                setSubmitButtonState={setSubmitButtonState}
                handleOtpFormSubmit={handleOtpFormSubmit}
              />
            )}

            {submitButtonState === 'newPassword' && (
              <NewPasswordForm
                handleNewPasswordFormSubmit={handleNewPasswordFormSubmit}
              />
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ForgotPassword;
