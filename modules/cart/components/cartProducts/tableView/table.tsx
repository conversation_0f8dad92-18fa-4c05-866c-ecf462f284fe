import useTranslation from 'next-translate/useTranslation';
import React, { useState } from 'react';

import { useRouter } from 'next/router';

import { useAppDispatch, useAppSelector } from 'store/hooks/index';
import { deleteCart } from 'store/slices/cartSlice';

import TableData from '@/modules/cart/components/cartProducts/tableView/tableData';
import { userAPI } from 'APIs';
import { toast } from 'react-toastify';
import Modal from '@/modules/common/modal';

const Table: React.FC = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { t } = useTranslation();
  const [modalOn, setModalOn] = useState(false);
  const [choice, setChoice] = useState(false);

  const cartData = useAppSelector(
    (state) => state.persistedReducer.cart.allCartItems
  );

  const handleDeleteAllCartItem = async () => {
    try {
      const res = await userAPI.deleteAllCartItem();
      if ('data' in res) {
        dispatch(deleteCart());
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  return (
    <>
      {modalOn && cartData?.length! > 0 && (
        <Modal
          setModalOn={setModalOn}
          setChoice={setChoice}
          trigger={handleDeleteAllCartItem}
          modalTitle={`Clear Cart`}
          bodyText={`${t('common:are_you_sure')}`}
        />
      )}
      <div className="container px-4 py-20">
        <div className="overflow-x-auto rounded-lg border">
          <div className="inline-block min-w-full">
            <table className="inline-table w-full text-left text-sm">
              <thead className="text-center">
                <tr className="border-b">
                  <th scope="col" className="px-5 py-4 capitalize">
                    {t('common:image')}
                  </th>
                  <th scope="col" className="px-5 py-4 capitalize">
                    {t('common:products')}
                  </th>
                  <th scope="col" className="px-5 py-4 capitalize">
                    Size
                  </th>
                  <th scope="col" className="px-5 py-4 capitalize">
                    Color
                  </th>
                  <th scope="col" className="px-5 py-4 capitalize">
                    {t('common:price')}
                  </th>
                  <th scope="col" className="px-5 py-4 capitalize">
                    {t('common:quantity')}
                  </th>
                  <th scope="col" className="px-5 py-4 capitalize">
                    {t('common:total')}
                  </th>
                  <th scope="col" className="px-5 py-4 capitalize">
                    {t('common:remove')}
                  </th>
                </tr>
              </thead>

              <tbody>
                <TableData />
              </tbody>
            </table>
            <div className="float-right mt-5 mb-4 flex gap-x-4 px-4">
              <button
                className="bg-black p-3 text-xs text-white transition-all duration-500 ease-in-out hover:bg-primary dark:bg-dark_primary dark:hover:border dark:hover:bg-black"
                onClick={() => {
                  router.push('/');
                }}
              >
                {t('common:continue_shopping').toUpperCase()}
              </button>
              <button
                className="bg-black py-3 px-7 text-xs text-white transition-all duration-500 ease-in-out hover:bg-primary dark:bg-dark_primary dark:hover:border dark:hover:bg-black"
                onClick={() => setModalOn(true)}
              >
                {t('cart:clear_cart').toUpperCase()}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Table;
