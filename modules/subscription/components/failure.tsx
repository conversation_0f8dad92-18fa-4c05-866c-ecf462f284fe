const FailureComponent: React.FC = () => {
  return (
    <div className="mt-36 flex items-center justify-center">
      <div className="flex flex-col items-center justify-center py-4">
        {/* <Image src="/failure.png" alt="Failed" width={125} height={168} /> */}
        <p className="font-caveat text-[32px] font-normal leading-[36px] text-red-600">
          Payment Failed
        </p>
        <p className="mb-4 text-gray-600">
          Your payment could not be processed.
        </p>
        <p className="text-gray-500">Please try again or contact support.</p>

        <div className="mt-6 flex gap-4">
          <button
            onClick={() => (window.location.href = '/pricing')}
            className="rounded-lg bg-[#1EA951] px-6 py-3 text-white transition-colors hover:bg-green-600"
          >
            Try Again
          </button>
          <button
            onClick={() => (window.location.href = '/')}
            className="rounded-lg border border-gray-300 px-6 py-3 text-gray-600 transition-colors hover:bg-gray-50"
          >
            Go Home
          </button>
        </div>
      </div>
    </div>
  );
};

export default FailureComponent;
