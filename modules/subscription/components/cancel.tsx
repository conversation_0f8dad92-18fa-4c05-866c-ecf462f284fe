const CancelComponent: React.FC = () => {
  return (
    <div className="mt-36 flex items-center justify-center">
      <div className="flex flex-col items-center justify-center py-4">
        {/* <Image src="/cancel.png" alt="Cancelled" width={125} height={168} /> */}
        <p className="font-caveat text-[32px] font-normal leading-[36px] text-yellow-600">
          Payment Cancelled
        </p>
        <p className="mb-4 text-gray-600">
          Your subscription was not activated.
        </p>
        <p className="text-gray-500">You can try again anytime.</p>

        <div className="mt-6 flex gap-4">
          <button
            onClick={() => (window.location.href = '/pricing')}
            className="rounded-lg bg-[#1EA951] px-6 py-3 text-white transition-colors hover:bg-green-600"
          >
            Choose Again
          </button>
          <button
            onClick={() => (window.location.href = '/')}
            className="rounded-lg border border-gray-300 px-6 py-3 text-gray-600 transition-colors hover:bg-gray-50"
          >
            Go Home
          </button>
        </div>
      </div>
    </div>
  );
};

export default CancelComponent;
