import { userAP<PERSON> } from 'APIs';
import { Form, Formik } from 'formik';
import { handleMediaUpload } from 'helper/handleMediaUpload';
import Image from 'next/image';
import { useState } from 'react';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useAppDispatch, useAppSelector } from 'store/hooks/index';
import { storeCustomerDetails } from 'store/slices/userSlice';

export interface Media {
  url: string;
  name: string;
  file: File;
}

function ProfilePictureForm() {
  const dispatch = useAppDispatch();
  const [media, setMedia] = useState<Media[]>([]);
  // console.log('media',media[0]?.url)

  const [file, setFile] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );
  const customer = useAppSelector(
    (state) => state.persistedReducer.user.customerDetails!
  );

  // console.log('user', customer?.image?.profile);

  const handleSubmit = async (values: any) => {
    try {
      setIsLoading(true);
      let mediaUrl: string[] = [];

      if (media.length > 0) {
        const fileData = {
          featureName: 'post',
          filenames: media.map((item) => item.name),
        };

        // console.log('file data from form', fileData);

        try {
          // Upload all media files together
          mediaUrl = await handleMediaUpload(
            fileData,
            media.map((item) => item.file),
            token,
            true
          );
        } catch (uploadError: any) {
          toast.error(uploadError.message || 'Media upload failed', {
            containerId: 'bottom-right',
          });

          return; // Stop execution if upload fails
        } finally {
          setIsLoading(false); // Stop loading
        }
      }

      const data = {
        image: {
          profile: mediaUrl[0],
        },
      };

      const res = await userAPI.updateCustomer(data);

      if (res.data) {
        // console.log('res data',res.data)
        dispatch(storeCustomerDetails(res!.data));
        toast.success('Update User Profile Successfully', {
          containerId: 'bottom-right',
        });
      } else {
        toast.error('Failed to Create Update User Profile', {
          containerId: 'bottom-right',
        });
      }
    } catch (error: any) {
      toast.error(error.message || 'Error creating post', {
        containerId: 'bottom-right',
      });
    }
  };

  return (
    <div className="flex flex-col items-center">
      <Image
        src={ media[0]?.url || customer?.image?.profile || '/user.png'}
        alt="profile Image"
        height={140}
        width={140}
      />

      <Formik
        initialValues={{
          files: [],
        }}
        onSubmit={(values, actions) => {
          handleSubmit(values);
          actions.setSubmitting(false);
        }}
      >
        {(formikprops) => {
          const addMedia = (event: React.ChangeEvent<HTMLInputElement>) => {
            const files = event.target.files;
            if (files && files.length > 0) {
              const file = files[0]; // Only take the first selected file

              if (!file.type.startsWith('image/')) {
                toast.error('Only images are allowed!', {
                  containerId: 'bottom-right',
                });
                return;
              }

              const fileUrl = URL.createObjectURL(file);

              setFile({
                [fileUrl]: { src: fileUrl, type: file.type, file: file },
              });

              // Replace media state instead of appending
              setMedia([{ url: fileUrl, name: file.name, file: file }]);

              // Update Formik values
              formikprops.setFieldValue('files', [file]);
              formikprops.setFieldValue('media', [
                { url: fileUrl, name: file.name, file },
              ]);

              // Automatically submit the form
              formikprops.submitForm();
            }
          };
          return (
            <Form onSubmit={formikprops.handleSubmit}>
              <div className="mt-2 flex w-full items-center justify-center rounded-md border bg-primary p-2 ">
                <input
                  className="hidden"
                  type="file"
                  id="media"
                  name="media"
                  onChange={addMedia}
                  accept="image/*,video/*"
                  multiple
                />
                <label
                  htmlFor="media"
                  className="flex cursor-pointer items-center gap-4"
                >
                  <span className="text-md font-medium text-[#04160A]">
                    Add Media
                  </span>
                </label>
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
}

export default ProfilePictureForm;
