interface props {
  value: string;
  label: string;
  verified?: boolean;
}

const SingleDetail: React.FC<props> = ({ value, label, verified }) => {
  return (
    <>
      <div className="flex flex-row">
        <span className="mr-2  font-semibold">{label}: </span>
        <span className="">
          {value}{' '}
          {(label === 'Phone' || label === 'Email') && !verified
            ? '(unverified)'
            : ''}
        </span>
      </div>
    </>
  );
};

export default SingleDetail;
