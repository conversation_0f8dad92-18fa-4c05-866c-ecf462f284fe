import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import React from 'react';

import WithAuth from '@/modules/auth/withAuth';
import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import AddressForm from '@/modules/myAccount/addresses/components/addressForm';
import SingleAddress from '@/modules/myAccount/addresses/components/singleAddress';
import { useState } from 'react';
import { useAppSelector } from 'store/hooks/index';

const AddressesComponent: React.FC = () => {
  const { t } = useTranslation();

  const [showAddAddress, setShowAddAddress] = useState('hidden');
  // const [showEditAddress, setShowEditAddress] = useState('hidden');

  const UserAddresses = useAppSelector(
    (state) => state.persistedReducer.UserAddress.addresses
  );

  const addButtonOnClick = () => {
    showAddAddress === '' ? setShowAddAddress('hidden') : setShowAddAddress('');
  };

  // const editButtonOnClick = () => {
  //   showEditAddress === ''
  //     ? setShowEditAddress('hidden')
  //     : setShowEditAddress('');
  // };

  return (
    <>
      <Breadcrumb
        title={t('manage-address:addresses')}
        pathArray={[`${t('common:home')}`, `${t('manage-address:addresses')}`]}
        linkArray={['/', '/myAccount/addresses']}
      />
      <div className="container mx-auto my-24 px-5 text-gray-800 dark:text-dark_text">
        <div className="mb-3 flex flex-wrap justify-between">
          <div className="text-3xl font-bold">{t('common:my_account')}</div>
          {/* <Link prefetch={false} href="/myAccount/form" passHref> */}
          <div className="mt-2 text-xl md:mt-0">
            <button
              className="rounded bg-primary py-2 px-4 text-sm text-white transition-all duration-500 ease-in-out hover:bg-black focus:outline-none dark:bg-dark_primary dark:hover:border"
              type="button"
              onClick={() => addButtonOnClick()}
            >
              {t('common:add')} +
            </button>
          </div>
          {/* </Link> */}
        </div>
        <hr />
        <div className="mx-auto mt-5 grid grid-cols-1 gap-4 md:grid-cols-3">
          <div className="md:col-span-1">
            <Link prefetch={false} href="/myAccount" passHref>
              <span className="cursor-pointer hover:text-primary dark:hover:text-dark_primary">
                {t('manage-address:return_to_account_details')}
              </span>
            </Link>
          </div>
          <div className="md:col-span-2">
            <div className={`${showAddAddress}`}>
              <AddressForm cancelForm={addButtonOnClick} />
              <hr className="my-2" />
            </div>
            <div className="text-left">
              <span className="text-4xl">
                {t('manage-address:your_addresses')}
              </span>

              {UserAddresses?.length > 0 && (
                <>
                  {UserAddresses?.map((UserAddress, index) => {
                    return (
                      <React.Fragment key={UserAddress?.id!}>
                        <SingleAddress singleAddress={UserAddress} />
                      </React.Fragment>
                    );
                  })}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default WithAuth(AddressesComponent);
