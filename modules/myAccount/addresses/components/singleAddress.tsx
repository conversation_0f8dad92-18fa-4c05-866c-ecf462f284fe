import { userAPI } from 'APIs';
import { UserAddress } from 'models';
import useTranslation from 'next-translate/useTranslation';
import { useState } from 'react';
import { useAppDispatch } from 'store/hooks/index';
import { deleteAddress } from 'store/slices/customerAddressSlice';

import LocationIcon from '@/modules/common/icons/location';
import PencilIcon from '@/modules/common/icons/pencil';
import TrashIcon from '@/modules/common/icons/trash';
import AddressForm from '@/modules/myAccount/addresses/components/addressForm';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface Props {
  singleAddress: UserAddress;
}

const SingleAddress: React.FC<Props> = ({ singleAddress }) => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();

  const [showEditAddress, setShowEditAddress] = useState('hidden');
  const [checked, setChecked] = useState(singleAddress.isDefault);
  const [showDefault, setShowDefault] = useState<boolean>(false);

  const editButtonOnClick = () => {
    showEditAddress === ''
      ? setShowEditAddress('hidden')
      : setShowEditAddress('');
  };

  const handleDeleteAddress = async (addressId: string) => {
    try {
      const res = await userAPI.deleteUserAddress(addressId);
      if ('data' in res) {
        dispatch(deleteAddress(addressId));
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  const address =
    singleAddress?.addressLine1 +
    ', ' +
    singleAddress?.city +
    ' - ' +
    singleAddress?.postCode;

  return (
    <>
      <div
        className="relative"
        onMouseEnter={() => {
          setShowDefault(true);
        }}
        onMouseLeave={() => {
          setShowDefault(false);
        }}
      >
        <div className="mt-3 w-3/4 text-sm lg:w-2/3">
          <div className="rounded-lg border p-6">
            <div className="flex flex-wrap items-center justify-between">
              <Link
                prefetch={false}
                href={`/myAccount/${singleAddress?.id}`}
                passHref
                className="cursor-pointer"
              >
                <div className="flex gap-x-3">
                  <LocationIcon />
                  <p>{singleAddress?.tag!}</p>
                </div>
              </Link>
              <div className="flex gap-x-3">
                <div
                  className="cursor-pointer hover:text-blue-600"
                  onClick={() => editButtonOnClick()}
                >
                  <PencilIcon height={4} width={4} />
                </div>
                <div
                  className="cursor-pointer hover:text-blue-600"
                  onClick={() => handleDeleteAddress(singleAddress?.id!)}
                >
                  <TrashIcon />
                </div>
              </div>
            </div>
            <p className="ml-0 md:ml-9">{address}</p>
          </div>
        </div>

        {showDefault && (
          <label className="absolute top-8 right-0 inline-flex cursor-pointer items-center lg:right-32 ">
            <input
              type="checkbox"
              value=""
              className="peer sr-only"
              checked={checked}
              onClick={() => {
                setChecked(!checked);
              }}
            />
            <div className="peer h-6 w-11 rounded-full bg-gray-200 after:absolute after:top-0.5 after:left-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white dark:border-gray-600  dark:bg-gray-700 dark:peer-checked:bg-dark_primary"></div>
            <span className="ml-3 hidden text-sm font-medium capitalize text-gray-900 dark:text-gray-300 md:block">
              {t('common:default')}
            </span>
          </label>
        )}
      </div>

      {/* <div className="mt-5">
          <span
            className="cursor-pointer hover:text-blue-600"
            onClick={() => editButtonOnClick()}
          >
            Edit{' '}
          </span>
          <span>| </span>
          <span
            className="cursor-pointer hover:text-blue-600"
            onClick={() => handleDeleteAddress(singleAddress?.id!)}
          >
            Delete
          </span>
        </div> */}
      {showEditAddress !== 'hidden' && (
        <>
          <div className={`${showEditAddress} w-full`}>
            <hr className="mt-5 mb-2" />
            <p className="my-5 font-bold">Edit Address</p>
            <AddressForm
              user={{
                firstName: singleAddress?.firstName,
                lastName: singleAddress?.lastName,
                addressLine1: singleAddress?.addressLine1,
                addressLine2: singleAddress?.addressLine2,
                city: singleAddress?.city,
                postCode: singleAddress?.postCode,
                phone: singleAddress?.phone,
                state: singleAddress?.state,
                country: singleAddress?.country,
                tag: singleAddress?.tag,
              }}
              id={singleAddress?.id}
              cancelForm={editButtonOnClick}
            />
            <hr className="my-2" />
          </div>
        </>
      )}
    </>
  );
};

export default SingleAddress;
