import useTranslation from 'next-translate/useTranslation';
import Image from 'next/legacy/image';

import { productInterface } from '@/modules/home/<USER>/models/index';
import myImageLoader from 'image/loader';

const SinglSlide = (props: { product: productInterface }) => {
  const { t } = useTranslation();

  const { title, description, deatils, image } = props.product;
  return (
    <div className="grid h-72 items-center dark:text-black sm:h-72 md:h-72 lg:h-80 xl:h-96 2xl:h-96">
      <div className="absolute inset-0 scale-100 md:inset-0">
        <Image loader={myImageLoader} src={image} alt="..." layout="fill" />
      </div>
      <div className="container z-10 mx-auto text-center font-[sans-serif] text-white md:pl-8 md:text-left lg:pt-4 xl:pl-4 ">
        <h1 className=" text-2xl font-bold sm:text-2xl md:text-3xl lg:text-left lg:text-4xl xl:text-5xl">
          {title}
        </h1>
        <h2 className="xl:text-4xlh text-xl font-thin text-white md:text-2xl lg:mt-3 lg:text-3xl 2xl:text-5xl">
          {description}
        </h2>
        <div className="mx-auto mt-1 w-3/4 text-sm font-thin md:ml-0 md:w-2/5 md:text-base lg:mt-4 lg:w-11/12">
          {deatils}
        </div>
        {/* <div className="mt-3 lg:mt-8">
          <button className="left-0 rounded-lg bg-primary py-2 px-4 text-xs font-bold text-white hover:bg-neutral-700 dark:bg-dark_primary sm:text-xs md:text-sm xl:text-base">
            {t('home:read_more').toUpperCase()}
          </button>
        </div> */}
      </div>
    </div>
  );
};

export default SinglSlide;
