import 'swiper/css';
import 'swiper/css/grid';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

import useTranslation from 'next-translate/useTranslation';
import { SwiperSlide } from 'swiper/react';

import { slideDetailsInterface } from '@/modules/home/<USER>/models';

import CarouselSlider from '@/modules/common/swiper/carouselSliderComponent';
import SinglSlide from '@/modules/home/<USER>/singleSlide.component';

const ImageSlider = () => {
  const { t } = useTranslation();

  const slideDetails: slideDetailsInterface[] = [
    {
      id: 1,
      image:
        'https://img.freepik.com/free-photo/muscular-man-doing-push-ups-using-dumbbells_7502-4776.jpg?w=900&t=st=1678185427~exp=1678186027~hmac=4ad3053805989a7e865b0e7323abe178810a4a3708458e3938929e860c344aea',
      description: `${t('home:slide_details.product_big_sale.description')}`,
      deatils: `${t('home:slide_details.product_big_sale.details')}`,
      title: `${t('home:slide_details.product_big_sale.title')}`,
    },
    {
      id: 2,
      image:
        'https://img.freepik.com/premium-photo/modern-fitness-gym-background-with-rows-professional-dumbbells-rows-benches_67155-25108.jpg?w=1380',
      title: `${t('home:slide_details.quality_products.title')}`,
      description: `${t('home:slide_details.quality_products.description')}`,
      deatils: `${t('home:slide_details.quality_products.details')}`,
    },
    {
      id: 3,
      image:
        'https://images.theconversation.com/files/396768/original/file-20210423-15-v2ycyo.jpg?ixlib=rb-1.1.0&rect=8%2C8%2C5982%2C3979&q=45&auto=format&w=926&fit=clip',
      title: `${t('home:slide_details.delivery.title')}`,
      description: `${t('home:slide_details.delivery.description')}`,
      deatils: `${t('home:slide_details.delivery.details')}`,
    },
  ];
  return (
    <div className="container mx-auto px-4">
      <CarouselSlider>
        {slideDetails?.map((data) => (
          <SwiperSlide key={data?.id}>
            <SinglSlide product={data} />
          </SwiperSlide>
        ))}
      </CarouselSlider>
    </div>
  );
};

export default ImageSlider;
