import React, { useEffect, useState } from 'react';

import useTranslation from 'next-translate/useTranslation';
import { useAppSelector } from 'store/hooks/index';
import { SwiperSlide } from 'swiper/react';

import PageContainer from '@/modules/common/layout/pageContainer';

import SwiperGrid from '@/modules/common/swiper/swipergrid';
import { UserProduct } from 'models';
import ProductRow from '../common/cycleProductGroup';
import NoItem from './noItem';
import { config } from 'config';

const TrendingProducts = () => {
  let [filterKey, setFilterKey] = useState('smartphones');
  let [filteredProduct, setProducts] = useState<UserProduct[]>([]);

  const { t } = useTranslation();
  const products = useAppSelector(
    (state) => state.persistedReducer.product.publicProducts
  );

  // Gets number of products to show
  const getMinimumProduct = () => {
    const w = window.innerWidth;
    if (w >= 980) return 10;
    if (w >= 768) return 6;
    return 4;
  };
  useEffect(() => {
    const newProduct = products?.filter(
      (product) => true || product.categories[0].name === 'smartphones'
    );
    setProducts(newProduct);
  }, [products]);

  const handleClick = (text: string) => {
    const newProduct = products?.filter(
      (product) => true || product.categories[0].name === text
    );
    setProducts(newProduct);
    setFilterKey(text);
  };

  return (
    <>
      <PageContainer className="relative mt-10 max-w-6xl">
        {config.showProduct === false ? (
          <div className="absolute inset-0 z-50 bg-neutral-50/70">
            <div className="flex h-screen items-center justify-around ">
              <div
                className="font-base  w-2/3 p-5 text-xl sm:w-auto"
                onClick={(e: React.MouseEvent<HTMLDivElement, MouseEvent>) =>
                  e.stopPropagation()
                }
              >
                Coming Soon...
              </div>
            </div>
          </div>
        ) : (
          ''
        )}

        {products?.length > 1 ? (
          <SwiperGrid
            slidesPerViewmobile={2}
            slidesPerView768={3}
            slidesPerView980={5}
            rows={1}
            loop={products.length > getMinimumProduct() ? true : false}
          >
            {products?.map((product: UserProduct, index: number) =>
              index % 2 === 1 ? (
                <React.Fragment key={product.id}>
                  <SwiperSlide key={Math.random() * 999999}>
                    <ProductRow
                      products={[products[index - 1], products[index]]}
                    />
                  </SwiperSlide>
                </React.Fragment>
              ) : index + 1 === products.length ? (
                <React.Fragment key={product.id}>
                  <SwiperSlide key={Math.random() * 999999}>
                    <ProductRow
                      products={[
                        products.length > getMinimumProduct()
                          ? products[0]
                          : products[index],
                      ]}
                    />
                  </SwiperSlide>
                </React.Fragment>
              ) : (
                ''
              )
            )}
          </SwiperGrid>
        ) : (
          // <NoItem />
          <></>
        )}
      </PageContainer>
    </>
  );
};

export default TrendingProducts;
