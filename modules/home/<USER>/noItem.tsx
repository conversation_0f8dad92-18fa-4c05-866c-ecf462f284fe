import Stripes from '@/modules/landingPage/assets/stripes';
import myImageLoader from 'image/loader';
import Image from 'next/image';

const NoItem = () => {
  return (
    <>
      <div className="relative mb-10 md:mt-20">
        {' '}
        {/* {releasetime ? (
          <h1>Fitsomnia is coming out soon</h1>
        ) : ( */}{' '}
        <div className="container mx-auto flex flex-col justify-center">
          {' '}
          <div className="w-[100%] text-center">
            <Stripes />
            <div className="p-6 md:p-20">
              <div className="flex flex-col items-center justify-center gap-2">
                <Image
                  src="/empty.png"
                  loader={myImageLoader}
                  alt="not-item-image"
                  height={150}
                  width={150}
                />{' '}
                <p className="font-base pb-2 text-black md:text-3xl">
                  No Product Found{' '}
                </p>{' '}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default NoItem;
