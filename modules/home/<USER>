import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useAppDispatch } from 'store/hooks/index';
import {
  setCartModalState,
  setLoginModalState,
  setModalState,
} from 'store/slices/modalSlice';

import TrendingProducts from '@/modules/home/<USER>/index';

const HomeComponent: React.FC = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setModalState(false));
    dispatch(setLoginModalState(false));
    dispatch(setCartModalState({ showModal: false }));
  }, [router.asPath]);

  return (
    <>
      <div className="scroll-smooth hover:scroll-auto">
        {/* <ImageSlider />
        <HomeShipping /> */}
        <TrendingProducts />
        {/* <div className="mb-4 md:mb-10">
        </div> */}
        {/* <div className="mb-4 md:mb-10">
          <BannerPage />
        </div> */}
        {/* <div className="mb-4 md:mb-10">
          <WeekDeals />
        </div> */}
        {/* <div className="container mx-auto mb-4 px-4 md:mb-10">
          <HomefullBanner />
        </div> */}
        {/* <div className="mb-4 md:mb-10">
          <BestSell />
        </div> */}
        {/* <div className="mb-4 md:mb-10">
          <FeaturedProducts />
        </div> */}
      </div>
    </>
  );
};

export default HomeComponent;
