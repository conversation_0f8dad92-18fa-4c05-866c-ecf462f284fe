const Stripes = () => {
  return (
    <>
      <svg
        width="1237"
        height="460"
        viewBox="0 0 1237 460"
        fill="none"
        className="absolute h-full w-full object-fill opacity-100 mix-blend-lighten"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g opacity="0.5" filter="url(#filter0_f_42_3019)">
          <path
            d="M-415.383 37.1156C-400.446 142.378 -351.047 240.784 -261.726 301.801C-149.126 378.717 -54.6949 407.632 -4.64049 544.91C45.7196 683.105 60.8757 828.813 203.439 907.127C336.437 980.331 543.206 957.968 615.187 809.508C683.586 668.386 614.662 509.531 616.846 362.338C617.851 294.463 640.782 226.108 703.022 191.166C793.609 140.063 903.676 183.741 998.238 148.013C1094.33 111.979 1169.76 21.348 1211.47 -69.5011C1254.28 -162.971 1241.22 -263.909 1141.33 -310.557C974.871 -388.434 780.986 -402.935 599.987 -410.972C405.753 -419.62 214.533 -399.965 23.7936 -364.411C-82.8231 -344.538 -199.398 -335.016 -292.126 -273.387C-385.814 -211.103 -427.526 -94.9214 -418.048 14.8838C-417.436 22.1342 -416.563 29.5594 -415.383 37.1156Z"
            stroke="white"
            stroke-width="0.934141"
            stroke-miterlimit="10"
          />
          <path
            d="M-231.501 -252.378C-316.104 -195.335 -353.449 -90.1163 -345.063 9.33733C-344.495 16.02 -343.752 22.8337 -342.791 29.691C-329.688 125.126 -285.356 214.272 -206.561 270.616C-106.889 341.985 -25.5183 372.778 20.9109 498.132C68.3446 625.452 87.9995 752.16 216.106 818.768C335.694 880.921 519.839 860.131 584.962 726.434C646.896 599.333 586.971 456.638 590.64 324.601C592.256 263.802 613.439 202.654 669.652 171.337C690.437 160.103 713.153 152.887 736.61 150.066C802.126 140.938 872.971 155.832 936.041 131.984C1022.48 99.4003 1090.09 17.9855 1127.35 -64.0408C1165.61 -148.076 1153.55 -238.052 1064.71 -281.293C913.547 -354.758 733.946 -370.962 567.36 -377.689C393.305 -384.59 221.958 -367.206 51.2667 -334.885C-44.1249 -316.759 -148.601 -307.805 -231.501 -252.378Z"
            stroke="white"
            stroke-width="0.934141"
            stroke-miterlimit="10"
          />
          <path
            d="M-170.79 -231.194C-246.483 -179.742 -279.329 -85.1802 -272.253 4.14023C-271.772 10.1677 -271.117 16.2389 -270.331 22.3974C-258.844 108.093 -219.753 187.935 -151.528 239.649C-64.6975 305.427 3.61403 338.054 46.4179 451.529C90.8379 567.973 115.035 675.856 228.684 730.584C334.82 781.687 496.427 762.468 554.693 643.578C610.119 530.454 559.06 403.876 564.302 287.301C566.573 233.578 586.14 179.68 636.195 151.901C654.774 141.964 675.032 135.552 695.946 132.989C754.473 124.821 817.456 137.75 873.626 116.391C950.367 87.2148 1010.2 14.8412 1043.05 -58.3185C1076.59 -132.963 1065.28 -211.976 987.798 -251.722C852.136 -321.257 686.904 -339.077 534.426 -344.362C380.551 -349.604 229.077 -334.447 78.1715 -305.446C-5.90764 -289.023 -97.9798 -280.855 -170.79 -231.194Z"
            stroke="white"
            stroke-width="0.934141"
            stroke-miterlimit="10"
          />
          <path
            d="M-110.296 -210.359C-177.035 -164.367 -205.469 -80.5499 -199.573 -1.40633C-199.136 3.92232 -198.612 9.29463 -197.913 14.7543C-188.173 90.7094 -154.236 161.292 -96.5379 208.333C-22.5919 268.608 32.3544 303.157 71.8388 404.576C112.983 510.275 141.723 599.115 241.176 642.007C333.773 682.015 472.885 664.5 524.294 560.373C573.256 461.182 531.326 350.721 537.921 249.477C540.804 202.873 558.493 156.182 602.651 132.116C618.969 123.454 636.731 117.844 655.064 115.562C706.472 108.268 761.812 119.275 811.036 100.362C878.125 74.6362 930.145 11.3038 958.579 -52.5089C987.668 -117.719 977.448 -185.9 910.97 -222.109C790.683 -287.974 639.864 -307.367 501.843 -311.08C368.944 -314.14 236.121 -302.287 105.864 -275.745C32.529 -261.244 -47.3133 -253.775 -110.296 -210.359Z"
            stroke="white"
            stroke-width="0.934141"
            stroke-miterlimit="10"
          />
          <path
            d="M-49.7154 -189.353C-107.457 -148.733 -131.48 -75.7916 -126.719 -6.78124C-126.413 -2.15142 -125.933 2.56573 -125.365 7.2829C-117.372 73.4543 -88.5884 134.865 -41.4604 177.145C19.6881 232.004 61.4437 268.3 97.3903 357.795C135.477 452.662 168.585 522.502 253.799 553.644C332.725 582.515 449.518 566.748 494.025 477.296C536.524 392.125 503.809 297.869 511.496 211.868C515.078 172.558 530.933 132.899 569.063 112.415C583.199 105.04 598.537 100.248 614.357 98.2631C658.646 91.8862 706.08 100.971 748.621 84.461C805.97 62.1855 850.215 7.8944 874.194 -47.1392C898.741 -102.959 889.481 -160.439 834.098 -192.891C728.967 -254.563 592.868 -275.529 468.824 -277.8C356.039 -279.696 243.392 -269.09 132.944 -246.177C71.0091 -233.467 3.3964 -226.828 -49.7154 -189.353Z"
            stroke="white"
            stroke-width="0.934141"
            stroke-miterlimit="10"
          />
          <path
            d="M10.9092 -168.343C-37.8348 -133.139 -57.4897 -70.986 -53.8645 -12.1524C-53.8645 -8.22145 -53.2967 -4.20312 -52.8162 -0.141114C-46.5704 56.2465 -22.8972 108.528 13.6609 146.004C61.9681 195.49 90.5769 233.359 122.855 311.017C157.796 395.053 194.835 445.806 266.335 465.286C331.327 483.019 426.02 468.824 463.713 394.266C499.704 323.029 476.031 244.977 485.334 174.263C489.702 141.854 503.678 109.577 535.781 92.7172C547.68 86.641 560.533 82.6521 573.781 80.9242C610.994 75.5082 650.915 82.6277 686.337 68.5635C734.077 49.7385 770.548 4.6634 790.115 -41.5037C809.683 -87.6708 801.821 -135.105 757.532 -163.495C667.513 -221.542 545.871 -243.73 436.066 -244.298C343.369 -245.149 250.859 -235.819 160.199 -216.476C109.446 -205.687 54.1499 -199.529 10.9092 -168.343Z"
            stroke="white"
            stroke-width="0.934141"
            stroke-miterlimit="10"
          />
          <path
            d="M71.4888 -147.335C31.7423 -117.503 16.4989 -66.2257 19.0758 -17.569C19.0758 -14.2932 19.5126 -10.9737 19.8183 -7.61054C24.1893 36.9941 41.2063 79.4231 68.8681 114.686C104.378 159.019 119.883 198.416 148.623 264.063C180.682 337.223 220.778 368.889 279.175 376.751C329.754 383.521 402.87 370.811 433.662 311.235C463.188 253.974 448.556 192.083 459.257 136.525C464.149 111.192 476.51 86.165 502.585 72.8871C512.271 68.1032 522.671 64.9313 533.378 63.4964C563.428 59.1286 595.662 64.1515 624.183 52.5333C662.183 37.2025 690.704 0.950235 705.991 -36.0446C721.278 -73.0394 714.246 -109.86 680.92 -134.188C605.883 -188.959 499.004 -212.152 403.394 -211.322C330.716 -211.138 258.276 -203.009 187.365 -187.081C148.055 -177.909 104.858 -172.362 71.4888 -147.335Z"
            stroke="white"
            stroke-width="0.934141"
            stroke-miterlimit="10"
          />
          <path
            d="M132.113 -126.37C101.539 -101.823 90.4881 -61.4651 91.7984 -22.9853C91.7984 -20.3646 91.7985 -17.744 92.2352 -15.1233C95.4402 19.7088 106.234 53.4165 123.858 83.6315C146.439 122.941 148.929 163.692 174.043 217.416C203.132 279.7 246.722 301.102 291.667 288.522C327.045 291.58 379.327 273.148 403.262 228.117C426.324 184.832 420.733 139.145 432.876 98.9622C438.423 80.7487 448.993 62.9283 469.041 53.3193C476.489 49.8022 484.426 47.4314 492.583 46.2872C515.557 42.662 540.06 45.8504 561.681 36.7218C589.94 24.7542 610.73 -2.36948 621.562 -30.3667C632.395 -58.364 626.585 -85.0509 604.092 -104.618C544.035 -156.944 451.919 -180.486 370.461 -177.909C317.978 -176.875 265.771 -170.035 214.794 -157.512C186.36 -150.174 155.655 -145.107 132.113 -126.37Z"
            stroke="white"
            stroke-width="0.934141"
            stroke-miterlimit="10"
          />
          <path
            d="M192.695 -105.317C171.074 -85.9683 164.479 -56.6607 164.654 -28.3141C164.654 -26.3922 164.654 -24.4704 164.654 -22.5486C165.702 4.5314 173.389 30.8253 178.849 52.533C187.934 87.6933 178.063 128.837 199.465 170.68C225.671 222.089 273.149 225.758 304.291 200.163C333.06 190.667 357.448 171.131 372.995 145.129C389.593 115.822 392.912 86.2956 406.627 61.3995C412.854 49.1217 423.085 39.3372 435.629 33.6643C440.866 31.4544 446.351 29.8852 451.964 28.9908C467.775 26.3265 484.635 27.5494 499.311 20.8668C517.917 12.3497 530.889 -5.68912 537.31 -24.7325C540.332 -33.2739 540.994 -42.4725 539.226 -51.3587C537.458 -60.2449 533.326 -68.4896 527.264 -75.2236C483.194 -126.283 404.967 -149.475 337.704 -144.583C305.268 -142.922 273.15 -137.349 242.05 -127.986C224.798 -122.395 206.54 -117.634 192.695 -105.317Z"
            stroke="white"
            stroke-width="0.934141"
            stroke-miterlimit="10"
          />
          <path
            d="M219.023 24.4563C212.589 55.0604 192.079 97.0591 209.945 127.082C233.617 167.448 285.47 153.43 302.747 114.76C322.377 71.0155 346.519 24.8132 398.453 14.3787C407.292 12.6101 416.571 11.9329 424.401 7.58836C441.343 -1.75912 445.287 -25.453 438.261 -43.2656C417.049 -96.7467 344.56 -117.902 291.127 -108.469C271.371 -104.97 251.339 -96.9267 238.888 -81.3408C214.608 -51.2193 226.35 -10.3013 219.023 24.4563Z"
            stroke="white"
            stroke-width="0.934141"
            stroke-miterlimit="10"
          />
        </g>
        <defs>
          <filter
            id="filter0_f_42_3019"
            x="-429.83"
            y="-423.467"
            width="1674.86"
            height="1381.54"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feGaussianBlur
              stdDeviation="5"
              result="effect1_foregroundBlur_42_3019"
            />
          </filter>
        </defs>
      </svg>
    </>
  );
};

export default Stripes;
