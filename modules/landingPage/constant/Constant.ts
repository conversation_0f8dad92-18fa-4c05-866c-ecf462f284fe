// Feature
import Diet from '@/modules/landingPage/assets/landing/feature/diet.png';
import DietLogo from '@/modules/landingPage/assets/landing/feature/diet_logo.png';
import Newsfeed from '@/modules/landingPage/assets/landing/feature/newsfeed.png';
import NewsfeedLogo from '@/modules/landingPage/assets/landing/feature/newsfeed_logo.png';
import Spot from '@/modules/landingPage/assets/landing/feature/spot.png';
import SpotLogo from '@/modules/landingPage/assets/landing/feature/spot_logo.png';
import Training from '@/modules/landingPage/assets/landing/feature/trainer.png';
import TrainingLogo from '@/modules/landingPage/assets/landing/feature/trainer_logo.png';

// Newspaper
import BanglaAwage from '@/modules/landingPage/assets/landing/news/bangla_ouage.png';
import BangladeshProtodin from '@/modules/landingPage/assets/landing/news/bangladesh_protidin.png';
import BusinessEye from '@/modules/landingPage/assets/landing/news/business_eye.png';
import DhakaPost from '@/modules/landingPage/assets/landing/news/dhaka_post.png';
import Ittafq from '@/modules/landingPage/assets/landing/news/ittefaq.png';
import Priyo from '@/modules/landingPage/assets/landing/news/priyo.png';
import ProthomBella from '@/modules/landingPage/assets/landing/news/prothom_bela.png';
import Somoy from '@/modules/landingPage/assets/landing/news/somoy_alo.png';

// Social Media
import Facebook from '@/modules/landingPage/assets/landing/footer/social/facebook.png';
import Instagram from '@/modules/landingPage/assets/landing/footer/social/instragram.png';
import Linkedin from '@/modules/landingPage/assets/landing/footer/social/linkedin.png';
import Twitter from '@/modules/landingPage/assets/landing/footer/social/twitter.png';
import Youtube from '@/modules/landingPage/assets/landing/footer/social/youtube.png';

export const navLinks = [
  { label: 'Home', link: '/' },
  { label: 'Newsfeed', link: '/post/newsfeed' },
  // { label: 'Plans', link: '/plans' },
  { label: 'Pricing', link: '/pricing' },
  // { label: 'Features', link: '/' },
  { label: 'About', link: '/about' },
  { label: 'Contact Us', link: '/contact' },
];

export const news = [
  {
    label: 'ittefaq',
    image: Ittafq,
    link: 'https://www.ittefaq.com.bd/713617/%E0%A6%B8%E0%A7%8D%E0%A6%AC%E0%A6%BE%E0%A6%B8%E0%A7%8D%E0%A6%A5%E0%A7%8D%E0%A6%AF%E0%A6%95%E0%A6%B0-%E0%A6%9C%E0%A7%80%E0%A6%AC%E0%A6%A8-%E0%A6%89%E0%A6%AA%E0%A6%B9%E0%A6%BE%E0%A6%B0-%E0%A6%A6%E0%A6%BF%E0%A6%A4%E0%A7%87-%E0%A6%AA%E0%A6%BE%E0%A6%B0%E0%A7%87-%E0%A6%AB%E0%A6%BF%E0%A6%9F%E0%A6%B8%E0%A7%8B%E0%A6%AE%E0%A6%A8%E0%A6%BF%E0%A7%9F%E0%A6%BE/ ',
  },
  {
    label: 'bangla_protidin',
    image: BangladeshProtodin,
    link: 'https://www.bd-pratidin.com/health-tips/2025/01/08/1070601/',
  },
  {
    label: 'dhakapost',
    image: DhakaPost,
    link: 'https://www.dhakapost.com/technology/334322/',
  },
  {
    label: 'shomoyeralo',
    image: Somoy,
    link: 'https://www.shomoyeralo.com/news/299507',
  },
  {
    label: 'priyo',
    image: Priyo,
    link: 'https://m.priyo.com/e/5269587-%E0%A6%B8%E0%A7%8D%E0%A6%AC%E0%A6%BE%E0%A6%B8%E0%A7%8D%E0%A6%A5%E0%A7%8D%E0%A6%AF-%E0%A6%AB%E0%A6%BF%E0%A6%9F%E0%A6%A8%E0%A7%87%E0%A6%B8%E0%A6%95%E0%A7%87%E0%A6%A8%E0%A7%8D%E0%A6%A6%E0%A7%8D%E0%A6%B0%E0%A6%BF%E0%A6%95-%E0%A6%B8%E0%A7%8B%E0%A6%B6%E0%A7%8D%E0%A6%AF%E0%A6%BE%E0%A6%B2-%E0%A6%AE%E0%A6%BF%E0%A6%A1%E0%A6%BF%E0%A6%AF%E0%A6%BC%E0%A6%BE-%E0%A6%AB%E0%A6%BF%E0%A6%9F%E0%A6%B8%E0%A7%8B%E0%A6%AE%E0%A6%A8%E0%A6%BF%E0%A6%AF%E0%A6%BC%E0%A6%BE',
  },
  {
    label: 'prothombella',
    image: ProthomBella,
    link: 'https://banglarawaz.com/news/14631/',
  },
  {
    label: 'banglawage',
    image: BanglaAwage,
    link: 'https://banglarawaz.com/news/14631/',
  },
  {
    label: 'business_eye',
    image: BusinessEye,
    link: 'https://www.businesseyebd.com/2025/01/05/%E0%A6%B8%E0%A7%8D%E0%A6%AC%E0%A6%BE%E0%A6%B8%E0%A7%8D%E0%A6%A5%E0%A7%8D%E0%A6%AF-%E0%A6%B8%E0%A6%9A%E0%A7%87%E0%A6%A4%E0%A6%A8-%E0%A6%AA%E0%A7%8D%E0%A6%B0%E0%A6%9C%E0%A6%A8%E0%A7%8D%E0%A6%AE%E0%A7%87/ ',
  },
];

export const features = [
  {
    logo: NewsfeedLogo,
    title: 'Share On Newsfeed',
    description:
      'Share your day, journey, achievements and everything with your fit buddies in the newsfeed. Share reels, images and posts.',
    image: Newsfeed,
  },
  {
    logo: DietLogo,
    title: 'Track Eating Habit',
    description:
      'The only thing that boost up your fitness journey is food ! fitsomnia helps you to track your eating habit including water tracker to be aligned with your plan.',
    image: Diet,
  },
  {
    logo: TrainingLogo,
    title: 'Your Digital Trainer',
    description:
      'Get custom workout according to your body type and your requirements. Get effective result within weeks as you follow the digital trainer.',
    image: Training,
  },
  {
    logo: SpotLogo,
    title: 'New Fitness Friends',
    description:
      'Find your gym spot who gives a hand.Meet with new fitness lover people, make friends & share moments with them',
    image: Spot,
  },
];

export const socials = [
  {
    logo: Facebook,
    title: 'facebook',
    link: 'https://www.facebook.com/p/Fitsomnia-Bangladesh-61557884841503/',
  },
  {
    logo: Youtube,
    title: 'youtube',
    link: 'https://www.youtube.com/channel/UCBWAdmuRpei_zrAx1mmK5uA/',
  },
  {
    logo: Instagram,
    title: 'instagram',
    link: 'https://www.instagram.com/fitsomnia_bangladesh/?hl=en',
  },
  {
    logo: Twitter,
    title: 'twitter',
    link: 'https://x.com/fitsomnia',
  },
  {
    logo: Linkedin,
    title: 'linkedin',
    link: 'https://www.linkedin.com/in/fit-somnia-15aa81271',
  },
];
