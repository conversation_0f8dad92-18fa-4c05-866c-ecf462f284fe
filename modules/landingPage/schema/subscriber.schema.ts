import { object, string } from 'yup';

export const subscriberSchema = object().shape({
  email: string()
    .required('Provide an email address')
    .test('test-name', 'Enter a valid email', function (value) {
      const emailRegex =
        /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;

      let isValidEmail = emailRegex.test(value!);
      if (!isValidEmail) {
        return false;
      }
      return true;
    }),
});
