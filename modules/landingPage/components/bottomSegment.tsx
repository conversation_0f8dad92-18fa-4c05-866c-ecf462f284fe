import { NextComponentType } from 'next';

const BottomSegment: NextComponentType = () => {
  return (
    <>
      {' '}
      <div className="bg-[#EAFBFF]">
        <div className="items-top relative grid h-60 grid-cols-7 gap-x-6 md:h-96 xl:h-[33rem] 2xl:h-[52rem]">
          <div className="col-span-3"></div>
        </div>
      </div>
      <div className="mt-5 flex flex-col items-center px-10 tracking-tighter md:mt-10 lg:px-60 xl:px-80 2xl:px-[32rem]">
        <input
          id="email"
          placeholder="Email"
          className="mt-7 w-full border-b border-black pb-3 text-center text-sm focus:outline-none lg:mt-12 2xl:text-lg"
        />

        <br />
        <button className="mt-0 mb-3 bg-[#89EAFF] px-10 py-2 text-xs font-medium text-fit_header_text lg:px-20 lg:py-3 xl:px-32 xl:py-4 2xl:px-40 2xl:py-6 2xl:text-lg">
          Subscribe
        </button>
      </div>
    </>
  );
};

export default BottomSegment;
