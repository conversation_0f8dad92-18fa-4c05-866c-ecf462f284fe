import myImageLoader from 'image/loader';
import { NextComponentType } from 'next';
import Image from 'next/image';
import Diet from '../assets/diet';

const SixthSegment: NextComponentType = () => {
  return (
    <>
      <div className="center">
        <div className="container mx-auto flex flex-row items-center justify-center">
          <div className=" flex w-full flex-col items-center justify-center gap-10 text-center md:w-[75%] md:flex-row lg:w-[85%] xl:w-[75%] ">
            <div className=" flex-1">
              <Image
                loader={myImageLoader}
                // src={'/Trainingprograms.svg'}
                src={'/diet.jpg'}
                alt="Trainingprograms logo"
                // className="h-4 w-10 md:h-10 md:w-24 xl:h-12 2xl:h-14 2xl:w-32"
                height={250}
                width={350}
                quality={100}
              />
              {/* <Diet /> */}
            </div>
            <div className=" flex-1 ">
              <div className=" h-auto w-full px-4 text-left">
                <h1 className="pb-6 text-center font-[Outfit] text-2xl text-[40px] font-bold leading-[50px] text-[#1C2134] md:text-left">
                  Diet
                </h1>

                <p className="pb-6 text-justify font-[Outfit] text-sm font-[400] leading-[168%] text-[#1C2134] md:text-left md:text-lg">
                  let Fitsomnia calorie tracker help you achieve your fitness
                  goals by tracking your daily caloric intake, making better
                  food choices, and maintaining a healthy weight. Navigate to
                  our easy to use calorie tracker feature that has a barcode
                  scanner so you can easily input the nutritional information of
                  the food you are consuming. You can set personalized goals
                  based on your age, weight, height, and activity level. This
                  can help you create a realistic plan to achieve your health
                  and fitness goals.{' '}
                </p>

                <p className="pb-6"> </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SixthSegment;
