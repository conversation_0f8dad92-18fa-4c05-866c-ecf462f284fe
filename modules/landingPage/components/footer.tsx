import myImageLoader from 'image/loader';
import { NextComponentType } from 'next';
import Image from 'next/image';

const Footer: NextComponentType = () => {
  const sendEmail = () => {
    window.open(`mailto:<EMAIL>`);
  };
  return (
    <>
      <div className="container relative mx-auto mb-10 px-4">
        <hr />
        <div className="absolute inset-0  opacity-80"></div>
        <div className="relative inset-0 flex h-40 flex-col gap-y-2 pt-5 text-sm sm:h-60 md:h-36 lg:h-28 2xl:h-[15rem] 2xl:pt-9">
          <div className="flex flex-col flex-wrap items-center justify-center gap-3 text-black lg:flex-row lg:justify-between lg:gap-x-3 xl:mt-2 2xl:mt-5 2xl:mb-4 2xl:gap-x-5">
            <div className=" flex flex-wrap justify-around gap-3">
              <p
                onClick={() => window.open('/terms-of-service?prev=landing')}
                className="cursor-pointer hover:text-primary"
              >
                Terms & Conditions
              </p>
              <p>|</p>
              <p
                onClick={() => window.open('/privacy-policy?prev=landing')}
                className="cursor-pointer hover:text-primary"
              >
                Privacy Policy
              </p>
            </div>
            <div className="flex flex-wrap items-center gap-3">
              <div>
                <button className="rounded-full border p-2">
                  <Image
                    loader={myImageLoader}
                    src={'/mail-icon.svg'}
                    alt="icn_social insta"
                    width={30}
                    height={30}
                    quality={100}
                  />
                </button>
              </div>
              <div className="">
                <p onClick={sendEmail} className="cursor-pointer text-primary">
                  <EMAIL>
                </p>
              </div>
            </div>
            <div className="mr-0 flex flex-wrap items-center justify-center gap-x-3 text-black lg:ml-10 xl:mt-1 2xl:mt-2 2xl:mb-4 2xl:gap-x-5">
              <button
                className="rounded-full border p-2"
                onClick={() =>
                  window.open(
                    'https://www.facebook.com/profile.php?id=100083267954753&mibextid=LQQJ4d'
                  )
                }
              >
                <Image
                  loader={myImageLoader}
                  src={'/fbIcon.png'}
                  alt="icn email"
                  width={30}
                  height={30}
                  quality={100}
                />
              </button>
              <button
                className="rounded-full border p-2"
                onClick={() =>
                  window.open(
                    'https://instagram.com/fitsomnia?igshid=YjNmNGQ3MDY='
                  )
                }
              >
                <Image
                  loader={myImageLoader}
                  src={'/instaIcon.png'}
                  alt="icn_social insta"
                  width={30}
                  height={30}
                  quality={100}
                />
              </button>
              <button
                className="rounded-full border p-2"
                onClick={() => window.open('https://twitter.com/fitsomnia')}
              >
                <Image
                  loader={myImageLoader}
                  src={'/twitterIcon.png'}
                  alt="icn_social_twt"
                  width={30}
                  height={30}
                  quality={100}
                />
              </button>
              <button
                className="rounded-full border p-3"
                onClick={() =>
                  window.open(
                    'https://www.linkedin.com/in/fit-somnia-15aa81271'
                  )
                }
              >
                <Image
                  loader={myImageLoader}
                  src={'/linkedin.png'}
                  alt="icn_social_ink"
                  width={25}
                  height={25}
                  quality={100}
                />
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Footer;
