import { userAPI } from 'APIs';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { NextComponentType } from 'next';
import { toast } from 'react-toastify';
import { subscriberSchema } from '../schema/subscriber.schema';
import Stripes from '../assets/stripes';

const EighthSegment: NextComponentType = () => {
  const handleSubmit = async (email: string) => {
    try {
      const res = await userAPI.joinWaitList(email);
      if ('data' in res) {
        toast.success('Join request submitted successfully.', {
          containerId: 'bottom-right',
        });
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  return (
    <>
      <div className="container relative mx-auto mt-20 p-4">
        <div className="flex flex-col items-center justify-center">
          {' '}
          <div
            className="rounded bg-cover bg-no-repeat text-center"
            style={{
              background:
                'linear-gradient(90.01deg, rgba(0, 140, 118, 0.45) -20.11%, rgba(108, 214, 93, 0.45) 99.99%), linear-gradient(0deg, #6AB557, #6AB557), linear-gradient(90.01deg, #008C76 -20.11%, #6CD65D 99.99%)',
            }}
          >
            <Stripes />
            <div className="relative py-16 px-5">
              <div>
                {' '}
                <h1 className="text-1xl pb-2 font-bold text-white">
                  Fitsomnia is available now. So what are you waiting for? Join
                  Fitsomnia today and be one of the first to experience this
                  game-changing fitness app. With Fitsomnia by your side, there
                  is no limit to what you can achieve!{' '}
                </h1>{' '}
                <p className=" text-white">
                  “making your fitness journey smooth”with this{' '}
                </p>{' '}
              </div>{' '}
              <div className="relative mt-4">
                {' '}
                <Formik
                  initialValues={{
                    email: '',
                  }}
                  onSubmit={(values, actions) => {
                    handleSubmit(values.email);
                    actions.setSubmitting(false);
                    actions.resetForm();
                  }}
                  validationSchema={subscriberSchema}
                >
                  {(formikprops) => {
                    return (
                      <Form onSubmit={formikprops.handleSubmit}>
                        <Field
                          id="email"
                          type="text"
                          name="email"
                          placeholder="Enter your e-mail address.."
                          className=" w-full rounded-md border-b border-black p-3 text-left text-xs focus:outline-none sm:w-1/2 md:text-base"
                        />{' '}
                        <button
                          style={{
                            background:
                              'linear-gradient(180deg, #03DDE0 0.03%, #03CCE0 15.62%, #1996F2 71.53%, #1965F2 98.14%), linear-gradient(321.25deg, #DF387C -33.39%, #FF602C 103.69%), linear-gradient(90deg, #FFE259 0%, #FFA751 100%)',
                          }}
                          className="absolute top-[50%] right-1 flex translate-y-[-50%] items-center justify-center rounded py-1 px-2 text-xs text-white sm:px-4 md:right-[26%] md:text-base"
                        >
                          Submit
                        </button>
                        <div className="errMsg absolute left-[30%] text-red-600">
                          <ErrorMessage name="email" />
                        </div>
                      </Form>
                    );
                  }}
                </Formik>
              </div>{' '}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EighthSegment;
