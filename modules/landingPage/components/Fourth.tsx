import GroupLogo from '@/modules/landingPage/assets/landing/middle/group-logo.png';
import Group from '@/modules/landingPage/assets/landing/middle/group.png';
import Image from "next/image";

export const MiddlePage = () => {
	return (
    <>
      <div className="mx-auto mt-24 flex w-3/4 flex-col items-center justify-center gap-5 px-2 md:flex-row-reverse">
        <div>
          <p className="flex flex-col text-right text-[64px] font-bold  xl:text-[96px] xl:font-black">
            <span className="text-[#67696B]">Socialize</span>
            <span className="text-[#008331]">Training</span>
            <span className="text-[#00471B]">Diet</span>
          </p>
          <p className="text-right  text-base  md:w-64 xl:w-96">
            Fitsomnia is a fitness brand{' '}
            <span className="font-bold">
              born in the vibrant, never sleeping city,
            </span>{' '}
            designed for those who channel their restlessness into their fitness
            journey. The name combines{' '}
            <span className="font-bold">
              &quot;fit&quot; for fitness and &quot;somnia&quot;
            </span>{' '}
            from insomnia, representing a dedication to health and the restless
            energy that fuels ambition. It&apos;s a community for the night
            owls, the early risers, and everyone in between who finds their
            motivation in the quiet hours, making fitness accessible anytime.
          </p>
        </div>
        <div className="relative">
          <Image
            className="grayscale sm:h-[650px] sm:w-[450px]"
            src={Group}
            alt="group"
          />
          <Image
            src={GroupLogo}
            className="absolute inset-0 m-auto h-52 w-52 opacity-75 md:h-56 md:w-56 lg:h-80 lg:w-80"
            alt="group logo"
          />
        </div>
      </div>
    </>
  );
};
