import Apple from '@/modules/landingPage/assets/landing/hero/app-store.png';
import Flag from "@/modules/landingPage/assets/landing/hero/flag.png";
import Google from "@/modules/landingPage/assets/landing/hero/google-store.png";
import Group from "@/modules/landingPage/assets/landing/hero/group.png";
import Image from "next/image";
import Link from "next/link";

export const HeroPage = () => {
	return (
		<>
			<div className="container grid justify-items-center mx-auto mt-10">
				<div className="flex items-center justify-between gap-1">
					<Image src={Flag} className="w-4 h-4 " alt="flag" />
					<p className="text-base lg:text-lg">The First Bangladeshi App For</p>
				</div>

				<p className="text-3xl font-bold text-center sm:text-6xl sm:my-2">
					Social Media, Health & Fitness
				</p>

				<p className="text-center sm:max-w-xl">
					Fitsomnia stands for the relentless pursuit of improvement,
					supporting those who find peace and challenge in their
					workouts.
				</p>
				<div className=" my-5 flex flex-col md:flex-row gap-4 md:justify-center md:items-center">
					<Link
						href="https://play.google.com/store/apps/details?id=com.fitsomnia.fitsomniaApp"
						className="w-[218px] h-[64px]">
						<Image src={Google} alt="google" />
					</Link>
					<Link
						href="https://apps.apple.com/us/app/fitsomnia-connect-train-sweat/id1670700147"
						className="w-[218px] h-[64px]">
						<Image src={Apple} alt="apple" />
					</Link>
				</div>

				<Image src={Group} alt="group" className="w-9/12" />
			</div>
		</>
	);
};
