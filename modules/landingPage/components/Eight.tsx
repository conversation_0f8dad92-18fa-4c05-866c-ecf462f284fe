import AppStore from '@/modules/landingPage/assets/landing/hero/app-store.png';
import Google from '@/modules/landingPage/assets/landing/hero/google-store.png';
import Group1 from '@/modules/landingPage/assets/landing/hero/group-image1.png';
import Group2 from '@/modules/landingPage/assets/landing/hero/group-image2.png';
import landing1 from '@/modules/landingPage/assets/landing/hero/landing-ss-1.png';
import landing2 from '@/modules/landingPage/assets/landing/hero/landing-ss-2.png';
import landing3 from '@/modules/landingPage/assets/landing/hero/landing-ss-3.png';

import Image from 'next/image';
import Link from 'next/link';

export const AppOverviewPage = () => {
  return (
    <>
      {/* laptop 1440px */}
      <div id="app-overview">
        <div className="relative mt-14 hidden sm:block">
          <div>
            <div className="bg-primary sm:py-3 lg:py-5">
              <div className="text-center">
                <p className="font-normal sm:text-base lg:text-lg xl:text-2xl">
                  The First Bangladeshi App For
                </p>
                <p className="font-bold sm:text-2xl lg:text-3xl xl:text-5xl ">
                  Social Media, Health & Fitness
                </p>
              </div>
            </div>
            <div className=" my-5 flex flex-col gap-4 sm:mx-auto sm:flex-row sm:justify-center md:w-1/2 md:items-center lg:w-1/2 xl:w-2/6">
              <Link href="https://play.google.com/store/apps/details?id=com.fitsomnia.fitsomniaApp">
                <Image src={Google} alt="google" />
              </Link>
              <Link href="https://apps.apple.com/us/app/fitsomnia-connect-train-sweat/id1670700147">
                <Image src={AppStore} alt="apple" />
              </Link>
            </div>
          </div>
          <div className="">
            <Image
              src={Group1}
              alt="group image"
              className="xl-[130px] xl-[150px] absolute  top-0 left-0 md:w-3/12  xl:ml-5 xl:h-[230px] xl:w-[244px]"
            />

            <Image
              src={Group2}
              alt="group image"
              className="xl-[130px] xl-[150px] absolute  top-0 right-0 md:w-3/12  xl:mr-7 xl:h-[230px] xl:w-[244px]"
            />
          </div>
        </div>

        {/* Mobile Screen  425px*/}
        <div className="mx-auto flex flex-col items-center justify-center sm:hidden ">
          <div className="relative flex h-[247px] w-[127px]  justify-center ">
            <Image
              src={landing1}
              className="relative -mr-10 "
              alt="landing 1"
            />
            <Image src={landing2} className="relative -mr-10" alt="landing 2" />
            <Image src={landing3} className="relative " alt="landing 3" />
          </div>
          <div className="flex w-1/2 flex-col  gap-4 ">
            <Link href="https://play.google.com/store/apps/details?id=com.fitsomnia.fitsomniaApp">
              <Image src={Google} alt="google" />
            </Link>
            <Link href="https://apps.apple.com/us/app/fitsomnia-connect-train-sweat/id1670700147">
              <Image src={AppStore} alt="apple" />
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};
