import myImageLoader from 'image/loader';
import { NextComponentType } from 'next';
import Image from 'next/image';
import Friend from '../assets/friend';

const FourthSegment: NextComponentType = () => {
  return (
    <>
      <div className="center mt-20 w-[100vw] md:w-full">
        <div className="container mx-auto flex flex-row items-center justify-center">
          <div className=" flex w-full flex-col items-center justify-center gap-10 text-center md:flex-row lg:w-[85%] xl:w-[75%]">
            <div className=" flex-1">
              {/* <Image
                loader={myImageLoader}
                src={'/friend.svg'}
                alt="friend logo"
                //className="h-4 w-10 md:h-10 md:w-24 xl:h-12 2xl:h-14 2xl:w-32"
                height={750}
                width={700}
                quality={100}
              /> */}
              {/* <Friend /> */}
              <Image
                loader={myImageLoader}
                // src={'/Trainingprograms.svg'}
                src={'/gym.jpg'}
                alt="Trainingprograms logo"
                // className="h-4 w-10 md:h-10 md:w-24 xl:h-12 2xl:h-14 2xl:w-32"
                // height={250}
                // width={350}
                // className="h-4 w-10 md:h-3/4 md:w-1/4 xl:h-1/4 2xl:h-3/5 2xl:w-3/5"
                height={250}
                width={350}
                quality={100}
              />
            </div>
            <div className=" flex-1 ">
              <div className=" h-auto w-full px-3 text-left">
                <h1 className="pb-6 text-center font-[Outfit] text-2xl text-[35px] font-bold leading-[50px] text-[#1C2134] md:text-left">
                  Make new friends at the <br /> gym with{' '}
                  <span className="text-[#47B042]">Fitsomnia</span>
                </h1>

                <p className="pb-5 text-justify	 font-[Outfit] text-sm font-[400] leading-[168%] text-[#1C2134] md:text-left md:text-lg">
                  With Fitsomnia, you can not only track your workouts and
                  progress, but also connect with like-minded individuals who
                  share their passion for fitness. By joining the Fitsomnia
                  community, you can expand your network of friends, find
                  workout buddies, and even participate in fitness challenges
                  together. So, if you are looking to meet new people who share
                  your fitness goals and interests, Fitsomnia is the perfect
                  platform for you! Do not be shy, reach out to fellow users,
                  participate in group activities, and let the app help you
                  create new and lasting friendships. Together, you can motivate
                  each other to stay committed to your fitness journeys and
                  achieve your goals. Give Fitsomnia a try and start making new
                  friends today!{' '}
                </p>

                <p className="pb-6"> </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default FourthSegment;
