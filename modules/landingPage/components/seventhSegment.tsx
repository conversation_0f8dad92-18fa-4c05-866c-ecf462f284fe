import { NextComponentType } from 'next';

const SeventhSegment: NextComponentType = () => {
  return (
    <>
      {/* <div className="bg-white bg-[url('/lines.svg')] bg-cover bg-no-repeat"> */}
      <div className="bg-white ">
        <div className="container mx-auto flex  h-[80vh] flex-col items-center justify-center ">
          {' '}
          {/* <div className="rounded bg-[url('/join-fitsomnia.png')] bg-center bg-no-repeat py-60 text-center sm:bg-cover md:bg-cover lg:p-56"> */}
          <div className="rounded bg-[url('/join-fitsomnia.png')] bg-center bg-no-repeat py-60 text-center sm:bg-cover md:bg-cover lg:p-56">
            <div className="bg-cover bg-no-repeat">
              <div className="">
                {' '}
                <h1 className="pb-2 text-center font-bold text-white sm:text-sm md:text-3xl">
                  Join <PERSON> today and make friends with other Fitness
                  enthusiasts{' '}
                </h1>{' '}
                <p className=" text-center text-white sm:text-sm">
                  We have a large number of collection in our Fitmarket. Explore
                  a world of Clothing, Supplements, Accessories and many more
                  item you need.{' '}
                </p>{' '}
              </div>{' '}
              {/* <div className=" relative mx-auto mt-4 w-[115px] sm:mt-2">
                {' '}
                <button
                  className="center absolute top-[50%] mt-8 flex h-[45px] translate-y-[-50%] items-center justify-center rounded-full px-5 py-1 text-white sm:mt-5 sm:h-[35px]"
                  style={{
                    background:
                      'linear-gradient(180deg, #03DDE0 0.03%, #03CCE0 15.62%, #1996F2 71.53%, #1965F2 98.14%), linear-gradient(321.25deg, #DF387C -33.39%, #FF602C 103.69%), linear-gradient(90deg, #FFE259 0%, #FFA751 100%)',
                  }}
                >
                  Find Now
                </button>
              </div>{' '} */}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SeventhSegment;
