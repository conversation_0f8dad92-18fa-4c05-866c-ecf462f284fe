import Link from 'next/link';
import { Fragment, useState } from 'react';
import { useAppSelector } from 'store/hooks';

const NavBar = () => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const [navLinks, setNavLinks] = useState([
    {
      label: 'Home',
      link: '/',
    },
    // {
    //   label: 'Fitmarket',
    //   link: '/market',
    // },
    {
      label: 'About',
      link: '/about',
    },
    {
      label: 'Blog',
      link: '/blogs',
    },
    {
      label: 'Contact Us',
      link: '/contact',
    },
  ]);

  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };
  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };
  return (
    <>
      <nav
        className="fixed top-0 left-0 z-20 w-full"
        style={{
          background:
            'linear-gradient(90.01deg, rgba(0, 140, 118, 0.45) -20.11%, rgba(108, 214, 93, 0.45) 99.99%), #6AB557',
        }}
      >
        <div className="mx-auto flex max-w-screen-xl flex-wrap items-center justify-between p-4">
          <Link
            prefetch={false}
            //prefetch={false}
            href="/"
            className="flex items-center text-white"
          >
            <span className="self-center whitespace-nowrap text-xl font-semibold dark:text-white md:text-2xl">
              <p>Fitsomnia</p>
            </span>
          </Link>
          <div className="flex md:order-2">
            <button
              type="button"
              className="mr-3 rounded-lg bg-white px-4 py-2 text-center text-sm font-medium text-black hover:bg-black hover:text-white focus:outline-none focus:ring-4 focus:ring-blue-300  dark:focus:ring-blue-800 md:mr-0"
            >
              <Link
                prefetch={false}
                //prefetch={false}
                href={token ? '/market' : '/account/sign-up'}
                className=""
              >
                Get started
              </Link>
            </button>
            <button
              data-collapse-toggle="navbar-sticky"
              type="button"
              onClick={toggleDrawer}
              className="inline-flex items-center rounded-lg p-2 text-sm text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600 md:hidden"
              aria-controls="navbar-sticky"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              <svg
                className="h-6 w-6"
                aria-hidden="true"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
          <div
            className="hidden w-full items-center justify-between md:order-1 md:flex md:w-auto"
            id="navbar-sticky"
          >
            <ul className="mt-4 flex flex-col rounded-lg border border-gray-100  p-4 font-medium dark:border-gray-700 dark:bg-gray-800 md:mt-0 md:flex-row md:space-x-8 md:border-0  md:p-0 md:dark:bg-gray-900">
              {navLinks.map((item) => {
                return (
                  <li key={item.label}>
                    <Link
                      prefetch={false}
                      //prefetch={false}
                      href={{
                        pathname: item.link,
                        query: { prev: 'landing' },
                      }}
                      passHref
                      className="block rounded py-2 pl-3 pr-4 text-white md:p-0 md:hover:text-black"
                    >
                      {item.label}
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>

        
        <div>
          <div
            className={` absolute z-40 w-full sm:w-1/2 lg:w-1/3 ${
              isDrawerOpen ? 'left-0' : 'left-[-650px]'
            } top-0 block h-full bg-gradient-to-r from-[#008c36] to-[#6CD65D] duration-500 dark:bg-gray-800 md:hidden `}
          >
            <div className=" flex justify-start gap-5 bg-gradient-to-r from-[#008c36] to-[#6CD65D] p-4 dark:bg-gray-900">
              <div>
                <button
                  onClick={closeDrawer}
                  type="button"
                  className="inline-flex items-center justify-center rounded-full bg-gray-300 p-2 text-gray-500 duration-300 hover:bg-gray-500 hover:text-gray-300 focus:outline-none focus:ring-inset dark:bg-gray-700 "
                >
                  <span className="sr-only">Close menu</span>
                  <svg
                    className="h-3 w-3"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
              <p className="text-2xl font-semibold text-white">Fitsomnia</p>
            </div>
            <ul className="h-[100vh] bg-white bg-[url('/lines.svg')] bg-cover bg-no-repeat p-5 dark:bg-gray-900">
              {' '}
              {navLinks.map((item) => {
                return (
                  <Fragment key={item.label}>
                    <li>
                      <Link
                        prefetch={false}
                        //prefetch={false}
                        href={item.link}
                        passHref
                        className="mt-1 block w-full rounded bg-gradient-to-r from-[#008c36] to-[#6CD65D] py-2 pl-3 pr-4 text-center text-white  hover:bg-gray-100  hover:text-black md:p-0"
                      >
                        {item.label}
                      </Link>
                    </li>
                  </Fragment>
                );
              })}
            </ul>
          </div>
        </div>
      </nav>
    </>
  );
};

export default NavBar;
