import { NextComponentType } from 'next';
import NavBar from '@/modules/landingPage/components/topNav';
import Illustration from '@/modules/landingPage/assets/ill';
import AppleBadge from '@/modules/landingPage/assets/a-badge';
import GoogleBadge from '@/modules/landingPage/assets/g-badge';
import Landing01 from '@/modules/landingPage/assets/landing01';
import { config } from 'config';
import myImageLoader from 'image/loader';
import Image from 'next/image';

const TopSegment1: NextComponentType = () => {
  return (
    <>
      <div className=" overflow-hidden">
        {/* <Landing01 /> */}
        <NavBar />
        {/* <div className=" left-[50%] top-[40%] mx-auto flex translate-x-[-50%] translate-y-[-50%] flex-row items-center justify-center md:top-[40%] 2xl:top-[54%]"> */}
        <div className="   mt-24 flex  flex-row items-center justify-center  ">
          <div className="  mx-auto flex  w-3/4 flex-col items-center justify-center gap-5 text-center md:flex-row ">
            
            <div className=" flex-1 ">
              <div className=" h-auto w-full text-left md:px-4">
                <h1 className="text-lg font-bold  text-[#1C2134] md:text-4xl">
                  Fitsomnia
                </h1>
                <h1 className="pb-3 text-sm font-bold text-[#47B042] md:text-xl lg:pb-6 lg:text-4xl">
                  Your Reliable Daily Fitness Partner
                </h1>
                <p className="pb-0 text-xs text-[#1C2134] lg:pb-6 lg:text-base">
                  Learn where your audience struggle as they navigate your
                  digital business. Pair this understanding of the individual
                  experience{' '}
                </p>
                <div>
                  <div className="mt-2 flex flex-row">
                    <button
                      className=""
                      onClick={() => window.open(config?.playstore!)}
                    >
                      <GoogleBadge/>
                    </button>
                    <button
                      className="ml-5"
                      onClick={() => window.open(config?.appstore!)}
                    >
                      <AppleBadge />
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex-1">
              {/* <Illustration /> */}
              <Image
                loader={myImageLoader}
                // src={'/Trainingprograms.svg'}
                src={'/main.jpg'}
                alt="Spot logo"
                //  className="h-4 w-10 md:h-3/4 md:w-3/4 xl:h-full 2xl:h-full 2xl:w-full"
                height={250}
                width={350}
                quality={100}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TopSegment1;
