import { userAP<PERSON> } from 'APIs';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { handleMediaUpload } from 'helper/handleMediaUpload';
import { PrivacyType } from 'models/post/enums.post.interface';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { useAppSelector } from 'store/hooks/index';

export interface Media {
  url: string;
  name: string;
  file: File;
}

function AddPostForm() {
  const router = useRouter();
  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );
  const [media, setMedia] = useState<Media[]>([]);
  const [file, setFile] = useState<any>({});

  const handleSubmit = async (values: any) => {
    try {
      let mediaUrls: string[] = [];

      if (media.length > 0) {
        const fileData = {
          featureName: 'post',
          filenames: media.map((item) => item.name),
        };

        // console.log('file data from form', fileData);

        try {
          // Upload all media files together
          mediaUrls = await handleMediaUpload(
            fileData,
            media.map((item) => item.file),
            token,
            false
          );
        } catch (uploadError: any) {
          toast.error(uploadError.message || 'Media upload failed');
          return; // Stop execution if upload fails
        }
      }

      // Separate images and videos
      const images = mediaUrls.filter((url) =>
        url.match(/\.(jpeg|jpg|png|gif)$/i)
      );
      const videos = mediaUrls.filter((url) =>
        url.match(/\.(mp4|mov|avi|mkv)$/i)
      );

      const data = {
        content: values.content,
        privacy: values.privacy,
        images: images.map((url) => ({ url })),
        videos: videos.map((url) => ({ url })),
      };

      // console.log('post data', data);

      const res = await userAPI.createPost(data);

      if (res.data) {
        toast.success("Post Created Successfully");
        router.push("/post/newsfeed");
      } else {
        toast.error("Failed to Create Post");
      }
    } catch (error: any) {
      toast.error(error.message || 'Error creating post');
    }
  };

  return (
    <div data-testid="hygen">
      <Formik
        initialValues={{
          content: '',
          privacy: PrivacyType.PUBLIC,
          files: [],
        }}
        onSubmit={(values, actions) => {
          handleSubmit(values);
          actions.setSubmitting(false);
        }}
        // validationSchema={}
      >
        {(formikprops) => {
          // const addMedia = (event: React.ChangeEvent<HTMLInputElement>) => {
          //   const files = event.target.files;
          //   if (files) {
          //     const currentFiles = event.target.form?.files || [];
          //     const newFiles = Array.from(files);

          //     newFiles.forEach((file) => {
          //       const fileUrl = URL.createObjectURL(file);
          //       setFile((prev: any) => ({
          //         ...prev,
          //         [fileUrl]: {
          //           src: fileUrl,
          //           type: file.type,
          //           file: file,
          //         },
          //       }));

          //       const mediaInfo = {
          //         url: fileUrl,
          //         name: file.name,
          //         file: file,
          //       };
          //       const updatedMedia = [...media, mediaInfo];
          //       setMedia(updatedMedia);
          //       formikprops.setFieldValue('media', updatedMedia);
          //     });

          //     // Update the files in form values
          //     const existingFiles = event.target.form?.files || [];
          //     const updatedFiles = [...Array.from(existingFiles), ...newFiles];
          //     formikprops.setFieldValue('files', updatedFiles);
          //   }
          // };

          const addMedia = (event: React.ChangeEvent<HTMLInputElement>) => {
            const files = event.target.files;
            if (files) {
              const newFiles = Array.from(files);

              // console.log('Selected files:', newFiles); // Debugging step

              newFiles.forEach((file) => {
                const fileUrl = URL.createObjectURL(file);

                setFile((prev: any) => ({
                  ...prev,
                  [fileUrl]: {
                    src: fileUrl,
                    type: file.type,
                    file: file,
                  },
                }));

                const mediaInfo = {
                  url: fileUrl,
                  name: file.name,
                  file: file,
                };

                setMedia((prevMedia) => {
                  const updatedMedia = [...prevMedia, mediaInfo];
                  // console.log('Updated media state:', updatedMedia); // Debugging step
                  return updatedMedia;
                });

                formikprops.setFieldValue('media', (prevMedia: Media[]) => [
                  ...prevMedia,
                  mediaInfo,
                ]);
              });

              // Update the files in form values
              formikprops.setFieldValue('files', (prevFiles: File[]) => [
                ...prevFiles,
                ...newFiles,
              ]);
            }
          };

          const removeMedia = (url: string) => {
            const updatedMedia = media.filter((item) => item.url !== url);
            setMedia(updatedMedia);

            formikprops.setFieldValue('media', updatedMedia);

            // Remove the file from the files state
            if (file[url]?.file) {
              formikprops.setFieldValue('files', (currentFiles: File[]) =>
                currentFiles.filter((f) => f !== file[url].file)
              );

              // Clean up the file state
              setFile((prev: any) => {
                const newState = { ...prev };
                delete newState[url];
                return newState;
              });
            }
          };
          return (
            <Form onSubmit={formikprops.handleSubmit}>
              <>
                <div className="mb-4">
                  <Field
                    type="text"
                    className="w-full p-2 placeholder-gray-600 outline-0"
                    id="content"
                    name="content"
                    placeholder="content"
                  />
                  <div className="errMsg text-red-600">
                    <ErrorMessage name="content" />
                  </div>
                </div>
              </>
              <div className="input-group mb-2">
                <select
                  name="privacy"
                  className="form-control"
                  value={formikprops.values.privacy}
                  onChange={formikprops.handleChange}
                >
                  <option value={PrivacyType.PUBLIC}>public</option>
                  <option value={PrivacyType.PRIVATE}>private</option>
                </select>
              </div>

              <div className="errMsg text-danger text-red-600">
                <ErrorMessage name="privacy" />
              </div>

              <div className="input-group mb-2">
                <input
                  className="form-control rounded-start border-black"
                  type="file"
                  id="media"
                  name="media"
                  onChange={addMedia}
                  accept="image/*,video/*"
                  multiple
                />
              </div>
              <div className="errMsg text-danger text-red-600">
                <ErrorMessage name="media" />
              </div>

              <div className="media-preview d-flex flex-wrap gap-2">
                {media.map((item, index) => (
                  <div
                    key={index}
                    className="position-relative"
                    style={{ width: '150px', height: '150px' }}
                  >
                    {file?.[item.url]?.type?.startsWith('image/') ? (
                      <Image
                        src={item.url}
                        alt="Preview"
                        width={150}
                        height={150}
                        style={{ objectFit: 'cover' }}
                        // loader={myImageLoader}
                      />
                    ) : (
                      <video
                        src={item.url}
                        style={{
                          width: '150px',
                          height: '150px',
                          objectFit: 'cover',
                        }}
                        controls
                      />
                    )}
                    <button
                      type="button"
                      className="btn btn-danger btn-sm position-absolute end-0 top-0"
                      onClick={() => removeMedia(item.url)}
                    >
                      <i className="bi bi-x"></i>
                    </button>
                  </div>
                ))}
              </div>

              <div className="flex flex-wrap justify-end sm:justify-end md:justify-between lg:justify-between xl:justify-between">
                <button
                  type="submit"
                  className="my-2 w-full rounded bg-primary py-2 capitalize text-white transition-all duration-500 ease-in-out hover:bg-black dark:bg-dark_primary sm:w-full md:w-1/4 lg:w-1/4 xl:w-1/4"
                >
                  Submit
                </button>
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
}

export default AddPostForm;
