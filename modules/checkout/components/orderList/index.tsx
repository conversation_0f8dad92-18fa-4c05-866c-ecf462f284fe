import { useState } from 'react';
import { useAppSelector } from 'store/hooks/index';

import CartTotal from '@/modules/checkout/components/orderList/cartTotal';
import ChevronDown from '@/modules/common/icons/chevronDown';
import ChevronUp from '@/modules/common/icons/chevronUp';
import ShoppingCart from '@/modules/common/icons/shoppingCart';
import useTranslation from 'next-translate/useTranslation';

const OrderList: React.FC = () => {
  const [dropdown, setDropdown] = useState(false);
  const cartData = useAppSelector(
    (state) => state.persistedReducer.cart.allCartItems
  );
  const checkoutSummary = useAppSelector(
    (state) => state.persistedReducer.checkoutSummary.summary
  );
  const { t } = useTranslation();

  const totalCartPrice = checkoutSummary
    ? checkoutSummary?.productCost
    : cartData?.reduce((total, data) => {
        return total + data?.product?.info?.price! * data.quantity;
      }, 0);

  return (
    <>
      <div className="hidden flex-initial sm:hidden md:hidden lg:block lg:w-2/5">
        <CartTotal />
      </div>

      <div className="my-7 flex flex-wrap justify-between border-gray-500 py-5 px-3 text-xs sm:block sm:px-10 md:block md:px-10 lg:hidden lg:px-5 xl:hidden xl:px-5">
        <div className="flex w-full flex-wrap justify-between sm:px-4 md:px-16">
          <div className="flex flex-wrap">
            <ShoppingCart />

            <button
              onClick={() => {
                setDropdown(!dropdown);
              }}
            >
              <div className="flex flex-wrap justify-between px-2 text-sm">
                {dropdown === true ? (
                  <>
                    <p>{t('checkout:hide_order_summary')}</p>
                    <ChevronUp />
                  </>
                ) : (
                  <>
                    <p>{t('checkout:show_order_summary')}</p>
                    <ChevronDown />
                  </>
                )}
              </div>
            </button>
          </div>
          <p className="text-xl font-semibold">${totalCartPrice}</p>
        </div>

        {dropdown && (
          <div className="w-full md:px-14">
            <CartTotal />
          </div>
        )}
      </div>
    </>
  );
};

export default OrderList;
