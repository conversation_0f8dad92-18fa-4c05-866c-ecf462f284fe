import { ErrorMessage, Field, Form, Formik } from 'formik';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store/hooks/index';
import {
  addToBillingInfo,
  deleteCheckoutInfo,
} from 'store/slices/checkoutSlice';

import { paymentSchema } from '@/modules/checkout/components/schemas/checkout.schema';
import ChevronLeft from '@/modules/common/icons/chevronLeft';
import Modal from '@/modules/common/modal';
import { userAPI } from 'APIs';
import { IPlaceOrderProductInfoReq } from 'models';
import { PaymentMethodEnum } from 'models/order/enum.order.interface';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';
import { deleteCart } from 'store/slices/cartSlice';
import { resetSummary } from 'store/slices/checkoutSummary';

interface FormData {
  shippingAddressPicked: string;
  firstName: string;
  lastName: string;
  phone: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  country: string;
  postCode: string;
  paymentMethod: string;
}

const PaymentDetails: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const router = useRouter();

  const [showLabel, setShowLabel] = useState(false);
  const [showShippingForm, setShowShippingForm] = useState(false);
  const [showCreditCardForm, setShowCreditCardForm] = useState(false);
  const [dropdownText, setDropdownText] = useState('Use a new address');
  const [tags, setTags] = useState<string[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethodEnum[]>([]);
  const [points, setPoints] = useState(0);
  const [orderData, setOrderData] = useState<FormData>();

  const [modalOn, setModalOn] = useState(false);
  const [choice, setChoice] = useState(false);

  const cartData = useAppSelector(
    (state) => state.persistedReducer.cart.allCartItems
  );

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const shippingInfo = useAppSelector(
    (state) => state.persistedReducer.checkout.shippingInfo
  );

  let usableCart: IPlaceOrderProductInfoReq[] = [];
  cartData.map((cartItem) => {
    const cart: {
      productId: string;
      quantity: number;
      photo: string;
      color: string;
      size: string;
    } = {
      productId: cartItem?.productId,
      quantity: cartItem?.quantity,
      color: cartItem?.color!,
      size: cartItem?.size!,
      photo: cartItem?.product?.photos![0].url!,
    };
    usableCart.push(cart);
  });

  const initialValues = {
    paymentMethod: '',
    shippingAddressPicked: '',
    firstName: '',
    lastName: '',
    phone: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    country: '',
    postCode: '',
  };

  const [update, setUpdate] = useState(initialValues);

  const handleModal = (data: FormData) => {
    if (data.paymentMethod === PaymentMethodEnum.Stripe) {
      setOrderData(data);
      setModalOn(true);
    } else {
      handlePaymentSubmit(data);
    }
  };

  const handlePaymentSubmit = (data: FormData) => {
    const shippingData = {
      firstName: data.firstName,
      lastName: data.lastName,
      email: shippingInfo?.email!,
      addressLine1: data.addressLine1,
      addressLine2: data.addressLine2,
      city: data.city,
      state: data.state,
      country: data.country,
      postCode: data.postCode,
      phone: shippingInfo?.phone!,
    };
    {
      data ? dispatch(addToBillingInfo(shippingData)) : null;
    }

    const obj = {
      billingAddress: {
        firstName: data.firstName || shippingInfo?.firstName!,
        lastName: data.lastName || shippingInfo?.lastName!,
        email: shippingInfo?.email!,
        addressLine1: data.addressLine1 || shippingInfo?.addressLine1!,
        addressLine2: data.addressLine2! || shippingInfo?.addressLine2!,
        city: data.city || shippingInfo?.city!,
        state: data.state! || shippingInfo?.state!,
        country: data.country || shippingInfo?.country!,
        postCode: data.postCode || shippingInfo?.postCode!,
        phone: shippingInfo?.phone!,
      },
      shippingAddress: {
        firstName: shippingInfo?.firstName!,
        lastName: shippingInfo?.lastName!,
        email: shippingInfo?.email!,
        addressLine1: shippingInfo?.addressLine1!,
        addressLine2: shippingInfo?.addressLine2!,
        city: shippingInfo?.city!,
        state: shippingInfo?.state!,
        country: shippingInfo?.country!,
        postCode: shippingInfo?.postCode!,
        phone: shippingInfo?.phone!,
      },
      paymentMethod:
        data.paymentMethod === 'COD'
          ? PaymentMethodEnum.COD
          : data.paymentMethod === 'Stripe'
          ? PaymentMethodEnum.Stripe
          : PaymentMethodEnum.Points,
      products: usableCart,
    };
    const res = userAPI.checkout(obj, router).then(async (response) => {
      if (response?.orderId) {
        await userAPI.deleteAllCartItem();
        dispatch(deleteCart());
        dispatch(deleteCheckoutInfo());
        dispatch(resetSummary());
        if (obj.paymentMethod === PaymentMethodEnum.Stripe) {
          router.push(response.paymentUrl);
        } else {
          router.push('/payment-success');
          toast.success('Order created successfully!', {
            containerId: 'bottom-right',
          });
        }
      }
    });
  };

  // const handlePreviousAddress = (detail: string, setFieldValue: Function) => {
  //   if (detail === 'Use a new address') {
  //     setShowLabel(true);
  //     setFieldValue('firstName', '');
  //     setFieldValue('lastName', '');
  //     setFieldValue('addressLine1', '');
  //     setFieldValue('addressLine2', '');
  //     setFieldValue('city', '');
  //     setFieldValue('postCode', '');
  //     setFieldValue('phone', '');
  //   } else {
  //     setShowLabel(false);
  //     const selectedAddress = addresses.find((address) => {
  //       return address.tag === detail;
  //     });
  //     setFieldValue('firstName', selectedAddress?.firstName);
  //     setFieldValue('lastName', selectedAddress?.lastName);
  //     setFieldValue('addressLine1', selectedAddress?.addressLine1);
  //     setFieldValue('addressLine2', selectedAddress?.addressLine2);
  //     setFieldValue('city', selectedAddress?.state);
  //     setFieldValue('postCode', selectedAddress?.postCode);
  //     setFieldValue('phone', selectedAddress?.phone);
  //   }
  // };
  // const handleSameAddress = () => {
  //   setShowShippingForm(false);
  // };

  const getPaymentMethods = async () => {
    try {
      const res = await userAPI.getPaymentMethods();
      if ('data' in res) {
        setPaymentMethods(res.data.list);
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  const getPoints = async () => {
    try {
      const res = (await userAPI.getUserPoints()) as any;
      if ('data' in res) {
        setPoints(res?.data?.points);
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  useEffect(() => {
    getPoints();
    getPaymentMethods();
  }, []);

  return (
    <>
      {modalOn && (
        <Modal
          setModalOn={setModalOn}
          setChoice={setChoice}
          trigger={() => handlePaymentSubmit(orderData!)}
          modalTitle={`Confirm Redirect`}
          bodyText={`${t('common:are_you_sure')}`}
        />
      )}
      <p className="mt-5 text-lg">Payment</p>
      <p className="text-sm text-gray-500">
        All transactions are secure and encrypted.
      </p>

      <Formik
        initialValues={initialValues}
        onSubmit={(values, actions) => {
          const data = {
            paymentMethod: values.paymentMethod,
            shippingAddressPicked: values.shippingAddressPicked,
            firstName: values.firstName,
            lastName: values.lastName,
            addressLine1: values.addressLine1,
            addressLine2: values.addressLine2,
            city: values.city,
            state: values.state,
            country: values.country,
            postCode: values.postCode,
            phone: shippingInfo?.phone!,
          };

          handleModal(data);
          actions.setSubmitting(false);
        }}
        validationSchema={paymentSchema}
      >
        {(formikprops) => {
          return (
            <>
              <Form onSubmit={formikprops.handleSubmit}>
                {/* credit card info div */}
                <div className="input-group pt-3">
                  <div
                    role="group"
                    aria-labelledby="my-radio-group"
                    className="rounded border border-gray-300 "
                  >
                    {paymentMethods.map((method) => {
                      return (
                        <>
                          {method === PaymentMethodEnum.Points ? (
                            <>
                              {' '}
                              <div className="m-3">
                                <div className="flex flex-wrap justify-between">
                                  <div>
                                    <Field
                                      type="radio"
                                      name="paymentMethod"
                                      id="paymentMethod"
                                      value={method}
                                    />
                                    <label
                                      htmlFor="paymentMethod"
                                      className="pl-2"
                                    >
                                      {method}
                                    </label>
                                  </div>
                                  <p className="rounded-md bg-[#AFE1AF] p-2 text-xs dark:bg-dark_primary dark:text-white">
                                    Currently you have {points.toFixed(2)}{' '}
                                    points
                                  </p>
                                </div>
                              </div>
                            </>
                          ) : (
                            <>
                              {' '}
                              <div className="m-3">
                                <Field
                                  type="radio"
                                  name="paymentMethod"
                                  id="paymentMethod"
                                  value={method}
                                />
                                <label htmlFor="paymentMethod" className="pl-2">
                                  {method}
                                </label>
                              </div>
                            </>
                          )}
                          <hr />
                        </>
                      );
                    })}

                    <div className="errMsg text-red-600">
                      <ErrorMessage name="paymentMethod" />
                    </div>
                  </div>
                </div>
                <div className="mt-5 mb-10 flex flex-col flex-wrap items-center gap-5 sm:flex-col md:flex-row lg:flex-row xl:flex-row">
                  <button
                    type="submit"
                    className="w-full rounded bg-black p-3 text-sm text-white dark:bg-dark_primary sm:w-full md:w-24"
                  >
                    Confirm
                  </button>

                  <div className="flex flex-wrap items-center">
                    <div className="text-decoration-none block items-center sm:block sm:items-center md:hidden lg:hidden xl:hidden">
                      <Link prefetch={false} href="/cart" passHref>
                        {<ChevronLeft />}
                      </Link>
                    </div>
                    <div className="text-decoration-none mb-2">
                      <Link prefetch={false} href="/cart" passHref>
                        {t('checkout:return_to_cart')}
                      </Link>
                    </div>
                  </div>
                </div>
              </Form>
            </>
          );
        }}
      </Formik>
    </>
  );
};

export default PaymentDetails;
