import useTranslation from 'next-translate/useTranslation';
import { useAppSelector } from 'store/hooks/index';

interface Props {
  setModal: Function;
}

const ContactDetails: React.FC<Props> = (props) => {
  // const shippingInfo = useAppSelector(
  //   (state) => state.persistedReducer.checkout.shippingInfo
  // );

  const checkoutInfo = useAppSelector(
    (state) => state.persistedReducer.checkoutSummary.summary
  );
  const currency = useAppSelector((state) => state.persistedReducer.currency);

  const { t } = useTranslation();

  const { setModal } = props;
  return (
    <>
      <div className="flex flex-col flex-wrap rounded-lg border px-5">
        <div className="my-3 flex flex-wrap justify-between text-sm lg:items-center xl:items-center">
          <div className="flex flex-col flex-wrap gap-0 sm:flex-col sm:gap-0 md:flex-row md:gap-6 lg:flex-row lg:gap-6 xl:flex-row xl:gap-6">
            <p className="text-gray-500">{t('checkout:contact')}</p>
            <p>{checkoutInfo?.shippingAddress?.phone}</p>
          </div>
          <button
            onClick={() => {
              const obj = {
                info: true,
                ship: false,
                pay: false,
              };
              setModal(obj);
            }}
            className="ml-10 text-sm"
            style={{ border: 'none' }}
          >
            {t('checkout:change')}
          </button>
        </div>
        <hr />
        <div className="my-3 flex flex-wrap justify-between text-sm lg:items-center xl:items-center">
          <div className="flex flex-col flex-wrap gap-0 sm:flex-col sm:gap-0 md:flex-row md:gap-6 lg:flex-row lg:gap-6 xl:flex-row xl:gap-6">
            <p className="text-gray-500">{t('checkout:ship_to')}</p>
            <p>
              {checkoutInfo?.shippingAddress?.addressLine1},{' '}
              {checkoutInfo?.shippingAddress?.city},{' '}
              {checkoutInfo?.shippingAddress?.postCode}
            </p>
          </div>
          <button
            onClick={() => {
              const obj = {
                info: true,
                ship: false,
                pay: false,
              };
              setModal(obj);
            }}
            className="ml-10 text-sm"
            style={{ border: 'none' }}
          >
            {t('checkout:change')}
          </button>
        </div>
        <hr />
        <div className="my-3 flex flex-col flex-wrap gap-0 text-sm sm:flex-col sm:gap-0 md:flex-row md:gap-6 lg:flex-row lg:gap-6 xl:flex-row xl:gap-6">
          <p className="text-gray-500">{t('checkout:method')}</p>
          <p>
            {t('checkout:standard')} .{' '}
            {Intl.NumberFormat(
              `${currency.currencyLanguage}-${currency.currencyStyle}`,
              {
                style: 'currency',
                currency: `${currency.currencyName}`,
              }
            ).format(checkoutInfo?.shippingCost!)}
          </p>
        </div>
      </div>
    </>
  );
};

export default ContactDetails;
