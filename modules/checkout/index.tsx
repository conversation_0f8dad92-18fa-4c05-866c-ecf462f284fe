import { useEffect, useState } from 'react';

import withAuth from '@/modules/auth/withAuth';
import CheckoutInformationComponent from '@/modules/checkout/components/informationPage';
import CheckoutPaymentComponent from '@/modules/checkout/components/paymentPage';
import ShippingPage from '@/modules/checkout/components/shippingPage';
import { useAppDispatch } from 'store/hooks';
import { resetSummary } from 'store/slices/checkoutSummary';

const CheckoutComponent: React.FC = () => {
  const [modal, setModal] = useState({
    info: true,
    ship: false,
    pay: false,
  });

  const dispatch = useAppDispatch();

  const resetCheckoutSummary = () => {
    dispatch(resetSummary());
  };

  useEffect(() => {
    resetCheckoutSummary();
  }, []);

  return (
    <>
      {modal.info ? <CheckoutInformationComponent setModal={setModal} /> : ''}
      {modal.ship ? <ShippingPage setModal={setModal} /> : ''}
      {modal.pay ? <CheckoutPaymentComponent setModal={setModal} /> : ''}
    </>
  );
};

export default withAuth(CheckoutComponent);
