import { userAPI } from 'APIs';
import { config } from 'config';
import { OrderByUserId } from 'models';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import WithAuth from '../auth/withAuth';
import ChevronDown from '../common/icons/chevronDown';
import ChevronUp from '../common/icons/chevronUp';
import FormComponent from './components/form';
import OrderTotal from './components/orderList/orderTotal';

const ReturnComponent = () => {
  const router = useRouter();
  const orderId = router.query.orderId as string;
  const [ready, setReady] = useState(false);

  const [collapse, setCollapse] = useState(true);
  // const [collapseContact, setCollapseContact] = useState(true);
  const [orderDetails, setOrderDetails] = useState<OrderByUserId>();

  const getSingleOrder = async () => {
    try {
      const singleOrderDetails = await userAPI.getOrderProduct(orderId!);
      if (singleOrderDetails) setOrderDetails(singleOrderDetails);
    } catch (error) {}
  };

  const sendEmail = () => {
    window.open(`mailto:${config.email}`);
  };

  useEffect(() => {
    if (router.isReady) {
      setReady(true);
      getSingleOrder();
    }
  }, [router.isReady]);

  if (!ready) return null;

  return (
    <>
      <div className="flex flex-wrap justify-center">
        <div
          className="my-10 mx-3 flex flex-col py-7"
          style={{ width: ' 35rem ', height: 'auto', background: '#f3f3f3' }}
        >
          <div
            onClick={() => setCollapse(!collapse)}
            className="mx-3 flex justify-between rounded-lg border border-white bg-primary p-2  text-center text-white hover:bg-black"
          >
            <p className="">Return Request</p>
            <div className="flex items-center justify-center">
              {/* <p>Status</p> */}
              {collapse ? <ChevronDown /> : <ChevronUp />}
            </div>
          </div>
          {!collapse && (
            <div className="w-full px-5 md:px-0">
              <OrderTotal orderDetails={orderDetails!} />
            </div>
          )}

          <FormComponent />

          <div
            onClick={sendEmail}
            className="mx-3 flex cursor-pointer justify-between rounded-lg border border-white bg-white p-2 text-center  text-black"
          >
            <p className="">Contact</p>
            <div className="flex items-center justify-center">
              <p>{config.email}</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default WithAuth(ReturnComponent);
