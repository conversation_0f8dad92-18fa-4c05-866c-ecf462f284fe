import { FC, useEffect, useState } from 'react';

import HorizontalProduct from '@/modules/common/product/horizontalProduct';
import { userAPI } from 'APIs';
import { IProductSearchSchema } from 'models';

const SearchItems: FC<{
  searchText: string;
  setTotalProducts: Function;
  currentPage: number;
  limit: number;
}> = ({ searchText, setTotalProducts, currentPage, limit }) => {
  const [products, setProducts] = useState<IProductSearchSchema[]>([]);
  const [stext, setStext] = useState('');
  const [sCurrentPage, setCurrentPage] = useState(1);
  const [sLimit, setLimit] = useState(limit);

  const getSearchedProducts = async () => {
    if (searchText) {
      const res = await userAPI.searchProducts(searchText, currentPage, limit);

      setProducts(res?.items!);
      setTotalProducts(res?.items?.length);
    } else setProducts([]);
  };

  useEffect(() => {
    if (stext != searchText) {
      getSearchedProducts();
      setStext(searchText);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchText]);

  useEffect(() => {
    if (sCurrentPage != currentPage) {
      getSearchedProducts();
      setCurrentPage(currentPage);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, limit]);
  return (
    <>
      {searchText.length > 0 &&
        products &&
        products?.map((product) => (
          <div key={product.id} className="mx-auto w-fit">
            {/* <VerticalProduct */}
            <HorizontalProduct key={product.id} product={product} />
          </div>
        ))}
    </>
  );
};

export default SearchItems;
