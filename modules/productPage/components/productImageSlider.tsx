import { Navigation, Thumbs } from 'swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
import SwiperClass from 'swiper/types/swiper-class';

import Image from 'next/legacy/image';
import { useState } from 'react';

import myImageLoader from 'image/loader';
import { Product, ProductPhoto } from 'models';
import { useAppSelector } from 'store/hooks';
interface SingleProduct {
  product: Product;
}

const ProductImagesSlider: React.FC<SingleProduct> = ({
  product,
}: SingleProduct) => {
  const [activeThumb, setActiveThumb] = useState<SwiperClass>();
  const currency = useAppSelector((state) => state.persistedReducer.currency);

  return (
    <>
      {/* <Swiper
        loop={true}
        spaceBetween={10}
        navigation={false}
        modules={[Navigation, Thumbs]}
        grabCursor={true}
        thumbs={{ swiper: activeThumb }}
        className="product-images-slider"
      >
        {product?.photos?.map((item: ProductPhoto, index: number) => (
          <SwiperSlide key={index}>
            <div className="mb-5">
              <Image
                loader={myImageLoader}
                src={item.url!}
                alt="product images"
                quality={100}
                width={600}
                height={600}
              />
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {product?.photos?.length! > 1 && (
        <div className="ml-8 w-4/5 md:ml-16">
          <Swiper
            onSwiper={() => setActiveThumb}
            loop={true}
            navigation={true}
            spaceBetween={10}
            slidesPerView={3}
            modules={[Navigation, Thumbs]}
            className="product-images-slider-thumbs"
          >
            {product?.photos?.map((item: ProductPhoto, index: number) => (
              <SwiperSlide key={index}>
                <div>
                  <Image
                    loader={myImageLoader}
                    src={item.url!}
                    alt="product images"
                    width={600}
                    height={600}
                  />
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      )} */}

      <Swiper
        loop={true}
        spaceBetween={10}
        navigation={true}
        modules={[Navigation, Thumbs]}
        thumbs={{
          swiper: activeThumb && !activeThumb.destroyed ? activeThumb : null,
        }}
      >
        {product?.photos?.map((item: ProductPhoto, index: number) => (
          <SwiperSlide key={index}>
            <div className="mb-5">
              <Image
                className="h-96 w-full"
                loader={myImageLoader}
                src={item.url!}
                alt="product images"
                quality={100}
                width={600}
                height={600}
              />
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {product?.photos?.length! > 1 && (
        <div className="ml-8 w-4/5 md:ml-16">
          <Swiper
            onSwiper={setActiveThumb}
            loop={true}
            navigation={true}
            spaceBetween={10}
            slidesPerView={4}
            modules={[Navigation, Thumbs]}
          >
            {product?.photos?.map((item: ProductPhoto, index: number) => (
              <SwiperSlide key={index}>
                <div className="cursor-pointer hover:border">
                  <Image
                    className="h-28 w-full"
                    loader={myImageLoader}
                    src={item.url!}
                    alt="product images"
                    quality={100}
                    width={600}
                    height={600}
                  />
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      )}
    </>
  );
};

export default ProductImagesSlider;
