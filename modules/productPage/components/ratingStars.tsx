import StarIcon from '@/modules/common/icons/starIcon';
import React from 'react';

interface Props {
  avgRating: number;
}

const RatingStars: React.FC<Props> = ({ avgRating }) => {
  const rating = [
    avgRating,
    avgRating - 1 >= 0 ? avgRating - 1 : 0.0,
    avgRating - 2 >= 0 ? avgRating - 2 : 0.0,
    avgRating - 3 >= 0 ? avgRating - 3 : 0.0,
    avgRating - 4 >= 0 ? avgRating - 4 : 0.0,
  ];

  console.log(rating);

  return (
    <div className="flex">
      {rating.map((star, index) => {
        console.log(star);
        return (
          <React.Fragment key={index}>
            {star == 0.0 ? (
              <StarIcon stroke="currentColor" />
            ) : star >= 1 ? (
              <StarIcon fill="currentColor" />
            ) : (
              <StarIcon half={true} stroke="currentColor" />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default RatingStars;
