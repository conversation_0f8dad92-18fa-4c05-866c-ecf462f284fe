import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useState } from 'react';

import { useAppDispatch, useAppSelector } from 'store/hooks/index';

import CartDropdown from '@/modules/cart/components/cartDropdown';
import HeartIcon from '@/modules/common/icons/heartIcon';
import Modal from '@/modules/common/modal';
import useTranslation from 'next-translate/useTranslation';
import { setModalState } from 'store/slices/modalSlice';
import { trimDescription } from 'helper/trim';

interface Properties {}

const HeaderAccount: React.FC<Properties> = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { t } = useTranslation();

  const [showCartDropdown, setShowCartDropdown] = useState(false);
  const [modalOn, setModalOn] = useState(false);
  const [modalOnWishlist, setModalOnWishlist] = useState(false);
  const [choice, setChoice] = useState(false);
  const [modalCmp, setModalCmp] = useState(false);

  const comparisonProducts = useAppSelector(
    (state) => state?.persistedReducer?.compare?.compareList?.items
  );

  const providerName = useAppSelector(
    (state) => state?.persistedReducer?.provider?.provider
  );

  const showCartDropDown = () => {
    setShowCartDropdown(!showCartDropdown);
  };

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const wishlistItems = useAppSelector(
    (state) => state.persistedReducer.product.wishlist
  );

  const customer = useAppSelector(
    (state) => state.persistedReducer.user.customerDetails
  );

  const user = customer?.name
    ? customer?.name
    : customer?.email
    ? customer?.email
    : customer?.phone;

  const handleLogout = async () => {
    // Import session manager dynamically to avoid circular dependencies
    const { sessionManager } = await import('../../../../../../utils/sessionManager');
    await sessionManager.logout();
  };

  const handleClickWishlist = () => {
    if (token) {
      router.push('/wishlist');
    } else {
      setModalOnWishlist(true);
    }
  };

  const links = [
    { name: `${t('common:register')}`, link: '/account/sign-up' },
    { name: `${t('common:login')}`, link: '/account/sign-in' },
    { name: `${t('common:wishlist')}`, link: '/wishlist' },
    { name: `${t('common:logout')}`, link: '/account/sign-in' },
    { name: `${user}`, link: '/myAccount' },
  ];
  return (
    <>
      {modalOn && (
        <Modal
          setModalOn={setModalOn}
          setChoice={setChoice}
          trigger={handleLogout}
          modalTitle={t('common:logout')}
          bodyText={t('common:are_you_sure')}
        />
      )}
      {modalOnWishlist && (
        <Modal
          setModalOn={setModalOnWishlist}
          setChoice={setChoice}
          modalTitle="You need to login first."
          bodyText="Proceed to login?"
        />
      )}

      <div className="flex flex-col gap-x-3 lg:flex-row lg:items-center">
        <span className="my-0 uppercase">
          {token !== '' ? (
            <div className="flex flex-wrap gap-2">
              <div
                className="group relative cursor-pointer normal-case"
                id="NavProfileDiv"
              >
                <p
                  className="hover:text-primary dark:text-dark_text dark:hover:text-dark_primary"
                  id="user-name"
                >
                  {trimDescription(links[4].name, 19)}
                </p>
                <div
                  id="navProfileDropdown"
                  className={`absolute -left-[20px] top-[24px] z-40 hidden overflow-hidden whitespace-nowrap bg-white  px-6 py-6 text-left shadow-lg transition-all duration-300 ease-in group-hover:inline-block dark:bg-dark_bg dark:text-dark_text`}
                >
                  <ul className="">
                    {/* <Link prefetch={false} href="/wishlist" passHref>
                      <li className="transition-all duration-100 ease-linear hover:text-primary dark:hover:text-dark_primary">
                        {t('common:wishlist')}
                      </li>
                    </Link> */}
                    {/* <li
                      className="transition-all duration-100 ease-linear hover:text-primary dark:hover:text-dark_primary"
                      onClick={() => {
                        comparisonProducts[0]
                          ? dispatch(setModalState(!modalCmp))
                          : toast.warning('Comparison list is empty.', {
                              containerId: 'bottom-right',
                            });
                      }}
                    >
                      {t('common:comparison')}
                    </li> */}
                    <Link prefetch={false} href="/myAccount" passHref>
                      <li className="transition-all duration-100 ease-linear hover:text-primary dark:hover:text-dark_primary ">
                        {t('common:profile')}
                      </li>
                    </Link>
                    <Link prefetch={false} href="/myAccount/addresses" passHref>
                      <li className="transition-all duration-100 ease-linear hover:text-primary dark:hover:text-dark_primary">
                        {t('common:manage_addresses')}
                      </li>
                    </Link>
                    <Link prefetch={false} href="/order" passHref>
                      <li className="transition-all duration-100 ease-linear hover:text-primary dark:hover:text-dark_primary">
                        {t('common:orders')}
                      </li>
                    </Link>

                    <hr className="my-2" />
                    <Link prefetch={false} href="#" passHref>
                      <li
                        onClick={() => setModalOn(true)}
                        className="transition-all duration-100 ease-linear hover:text-primary dark:hover:text-dark_primary"
                      >
                        {links[3].name}
                      </li>
                    </Link>
                  </ul>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-wrap">
              <div className="cursor-pointer transition-all duration-100 ease-linear hover:text-primary dark:text-dark_text dark:hover:text-dark_primary">
                <Link prefetch={false} href={links[0].link}>
                  {links[0].name}
                </Link>
              </div>
              <span className="mx-1 dark:text-dark_text">/</span>
              <div className="cursor-pointer transition-all duration-100 ease-linear hover:text-primary dark:text-dark_text">
                <Link prefetch={false} href={links[1].link}>
                  {links[1].name}
                </Link>
              </div>
            </div>
          )}
        </span>
        <div className="flex cursor-pointer flex-row items-center gap-x-1">
          <button type="button" onClick={handleClickWishlist}>
            <div
              className={`${
                router.pathname === '/wishlist'
                  ? 'stroke-primary dark:stroke-dark_primary'
                  : 'stroke-black dark:stroke-dark_text'
              } flex fill-white text-base hover:stroke-primary hover:text-primary dark:fill-dark_bg dark:text-dark_text  dark:hover:stroke-dark_primary`}
            >
              <HeartIcon height="h-6" width="w-6" />
              <div
                className={
                  router.pathname === '/wishlist'
                    ? 'text-primary dark:text-dark_primary'
                    : ''
                }
              >
                {wishlistItems?.items?.length! > 0 && token !== ''
                  ? wishlistItems?.items?.length
                  : 0}
              </div>
            </div>
          </button>
          <span
            className="z-30 mt-2 cursor-pointer text-sm"
            onClick={(e) => {
              if (document.body.clientWidth >= 1024) showCartDropDown();
              else {
                router.push('/cart');
              }
            }}
          >
            <CartDropdown />
          </span>
        </div>
      </div>
    </>
  );
};

export default HeaderAccount;
