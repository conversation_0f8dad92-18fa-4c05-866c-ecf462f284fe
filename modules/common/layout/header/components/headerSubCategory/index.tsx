import Link from 'next/link';
import React, { useState } from 'react';

import MinusSolidIcon from '@/modules/common/icons/minusIcon';
import PlusSolidIcon from '@/modules/common/icons/plusIcon';
import { SubCategoryList } from 'models';

interface Props {
  category: SubCategoryList;
  showSub?: boolean;
  level: number;
}

const HeaderSubCategory: React.FC<Props> = ({
  category,
  showSub,
  level,
}: Props) => {
  const [showSubCategory, setShowSubCategory] = useState(showSub);
  const [expand, setExpand] = useState<boolean>(false);

  const handleExpandClick = (
    e: React.MouseEvent<HTMLSpanElement, MouseEvent>
  ) => {
    e.stopPropagation();
    setExpand(!expand);
  };

  return (
    <>
      {category ? (
        <div
          className="w-full"
          /** For hover effect only in large screen */
          // onMouseEnter={() =>
          //   document.body.clientWidth > 1023 && setShowSubCategory(true)
          // }
          // onMouseLeave={() => {
          //   document.body.clientWidth > 1023 && setShowSubCategory(false);
          // }}
        >
          <div className="flex cursor-pointer flex-row items-center justify-between px-3 py-2 text-sm transition-all duration-100 ease-linear hover:text-primary dark:hover:text-dark_primary">
            <span className="grow">
              <Link
                prefetch={false}
                href={{
                  pathname: `/collections/${category.name}`,
                  query: {
                    categoryId: category.id,
                    name: category.name,
                  },
                }}
                className={`${`ml-` + level} lg:ml-0`}
              >
                {category.name}
              </Link>
            </span>

            <span
              className="block"
              onClick={(e: React.MouseEvent<HTMLSpanElement, MouseEvent>) =>
                handleExpandClick(e)
              }
            >
              {category.subCategories ? (
                expand ? (
                  <MinusSolidIcon size={5} />
                ) : (
                  <PlusSolidIcon size={5} />
                )
              ) : (
                ''
              )}
            </span>
          </div>
          <div className={``}>
            {category.subCategories && expand ? (
              <ul className="pl-2">
                {category.subCategories.map((category: SubCategoryList) => (
                  <li key={category.name}>
                    <HeaderSubCategory category={category} level={level + 1} />
                  </li>
                ))}
              </ul>
            ) : (
              ''
            )}
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};
export default HeaderSubCategory;
