import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import BottomNavigationBar from '@/modules/common/layout/header/components//bottomNavigationBar';
import Navbar from '@/modules/common/layout/header/components//navbar';
import Drawer from '@/modules/common/layout/header/components/drawer';
import HeaderAccount from '@/modules/common/layout/header/components/headerAccount';
import Search from '@/modules/common/layout/header/components/search';
import myImageLoader from 'image/loader';
import Image from 'next/image';

const Header: React.FC = () => {
  const [showUser, setShowUser] = useState<boolean>(false);
  const [showAllCategory, setShowAllCategory] = useState<boolean>(false);
  const [drawer, setDrawer] = useState<boolean>(false);
  const [stickyClass, setStickyClass] = useState<string>('relative');

  const { pathname } = useRouter();
  const { t } = useTranslation();

  const toggleOpen = () => {
    setShowAllCategory(!showAllCategory);
  };

  const closeDrawer = () => {
    setDrawer(false);
  };

  const openDrawer = () => {
    setDrawer(true);
  };

  const setStickyNavbar = () => {
    if (window !== undefined) {
      let windowHeight = window.scrollY;
      if (windowHeight >= 140) {
        setStickyClass(
          'lg:fixed lg:top-0 lg:left-0 lg:z-40 lg:bg-white/95 dark:lg:bg-dark_bg lg:w-full lg:shadow-lg'
        );
        setShowUser(true);
        setShowAllCategory(false);
      } else {
        setStickyClass('relative');
        setShowUser(false);
      }
    }
  };

  useEffect(() => {
    window.addEventListener('scroll', setStickyNavbar);

    return () => {
      window.removeEventListener('scroll', setStickyNavbar);
    };
  }, []);

  // put the pathname in 'includes' where header needs to be hidden
  if (pathname.includes('/checkout')) {
    return null;
  }

  return (
    <>
      {/* Top portion */}
      {/* <header className="hidden justify-center border-b border-slate-200 py-2 lg:flex">
        <div className="container flex justify-between px-4 text-sm">
          <div className="space-x-2">
            <Language />
            <span>|</span>
            <Currency />
            <span>|</span>
            <ThemeChanger />
          </div>
          <div className="space-x-3"></div>
        </div>
      </header> */}

      {/* Middle portion */}
      <div className="mb-2 flex justify-center py-4 lg:pt-8 lg:pb-6">
        <div className="container flex items-center justify-center px-4 md:justify-between">
          <span className="text-2xl font-bold">
            <div className=" flex items-center justify-center gap-x-2">
              <Link prefetch={false} href="/">
                <Image
                  src="/fitsomnia-icon.png"
                  height={57}
                  width={57}
                  alt="Fitsomnia Logo"
                  loader={myImageLoader}
                  quality={100}
                />
              </Link>
              <div>
                {/* <a>BS Commerce</a> */}
                <p>Marketplace by</p>
                <p>Fitsomnia</p>
              </div>
            </div>
          </span>
          <span className="hidden w-2/5 lg:inline-block lg:w-[479px]">
            <Search placeholder={t('common:search_placeholder')} />
          </span>
          <span className="hidden lg:inline-block">
            <HeaderAccount />
          </span>
          {/* <span
          className="border border-gray-700 p-1 lg:hidden"
          onClick={() => setDrawer(!drawer)}
          id="menuToggler"
        >
          <MenuIcon />
        </span> */}
        </div>
      </div>

      {/* Navbar */}
      <Navbar
        drawer={drawer}
        setShowAllCategory={setShowAllCategory}
        showAllCategory={showAllCategory}
        showUser={showUser}
        stickyClass={stickyClass}
        toggleOpen={toggleOpen}
      />

      <Drawer drawer={drawer} closeDrawer={closeDrawer} />
      <BottomNavigationBar openDrawer={openDrawer} />
    </>
  );
};

export default Header;
