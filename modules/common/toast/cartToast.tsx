import myImageLoader from 'image/loader';
import { Product, UserProduct, WishlistProduct } from 'models';
import useTranslation from 'next-translate/useTranslation';
import Image from 'next/legacy/image';
import Link from 'next/link';
import ButtonType1 from '../buttons/buttonType1';
import CheckIcon from '../icons/checkIcon';
import { trimDescription } from 'helper/trim';

interface Props {
  product: Product | UserProduct | WishlistProduct;
}

const CartToast: React.FC<Props> = ({ product }: Props) => {
  const { t } = useTranslation();

  return (
    <>
      <div className="grid w-fit grid-cols-3">
        <div className="col-span-1">
          {product?.photos![0].url ? (
            <Image
              loader={myImageLoader}
              src={product?.photos![0].url!}
              alt={product?.photos![0].alt || 'product image'}
              width={80}
              height={80}
            />
          ) : (
            'Problem Rendering Image'
          )}
        </div>
        <div className="col-span-2 sm:px-4">
          <span className="mb-2">
            {trimDescription(product?.info.name, 15)}
          </span>
          <div className="mb-3 flex flex-row text-primary dark:text-dark_primary">
            <CheckIcon />
            {t('common:added_to_cart_successfully')}
          </div>
          <div className="ml-1 grid w-max grid-cols-2 text-xs sm:flex-row sm:text-xs">
            <div className="pb-2 pr-2 sm:pb-0 ">
              <Link prefetch={false} href="/cart" passHref>
                <ButtonType1
                  className="rounded uppercase"
                  text={t('common:view_cart')}
                />
              </Link>
            </div>
            <div>
              <Link prefetch={false} href="/checkout" passHref>
                <ButtonType1
                  className="rounded uppercase"
                  text={t('common:checkout')}
                />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CartToast;
