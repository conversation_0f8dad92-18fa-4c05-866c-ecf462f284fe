import React from 'react';
interface Props {
  onClickFunction?: Function;
  text?: string;
  disabled?: boolean;
  className?: string;
}

const ButtonType1: React.FC<Props> = ({
  onClickFunction,
  text,
  disabled,
  className,
}) => {
  return (
    <>
      {onClickFunction ? (
        <button
          className={`w-full bg-slate-300 px-1 py-2 transition-all duration-500 ease-in-out hover:bg-primary hover:text-white dark:text-black dark:hover:bg-dark_primary dark:hover:text-white ${className}`}
          onClick={() => {
            onClickFunction && onClickFunction();
          }}
          disabled={disabled}
        >
          {text}
        </button>
      ) : (
        <button
          className={`w-full bg-slate-300 px-1 py-2 transition-all duration-500 ease-in-out hover:bg-primary hover:text-white dark:text-black dark:hover:bg-dark_primary dark:hover:text-white ${className}`}
          disabled={disabled}
        >
          {text}
        </button>
      )}
    </>
  );
};

export default ButtonType1;
