import { FC } from 'react';

interface ICheckCircleOutline {
  size?: number;
  fill?: string;
}

const CircleIcon: FC<ICheckCircleOutline> = ({ size, fill }) => {
  return (
    <>
      {' '}
      <span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill={fill}
          stroke={fill?.toLowerCase() === '#ffffff' ? 'black' : fill}
          viewBox="0 0 20 20"
          height={size}
          width={size}
        >
          <circle cx="10" cy="10" r="9" />
        </svg>
      </span>
    </>
  );
};

export default CircleIcon;
