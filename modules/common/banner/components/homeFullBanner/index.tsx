import useTranslation from 'next-translate/useTranslation';
import { FC } from 'react';

import BannerHeading from '@/modules/common/banner/components/bannerHeading';
import Banner from '@/modules/common/banner/components/bannerWrapper';

const HomefullBanner: FC = () => {
  const { t } = useTranslation();

  return (
    <div className="bg-[url('https://img.freepik.com/premium-photo/3d-rendering-black-dumbbells-floor-dark-concept-fitness-room-with-training-equipments-back_67155-14961.jpg')] bg-cover bg-center bg-no-repeat dark:text-black">
      <Banner
        position="relative"
        width="w-full"
        height="h-52 sm:h-52 md:h-52 lg:h-80"
        // hasButton={true}
        // buttonText={t('home:discover_now')}
        linkhref="/deals" //need to modify href later
        buttonPosition=" "
        buttonEdge="rounded-lg"
        buttonPadding="px-3 sm:px-3 md:px-9 lg:px-9 xl:px-9 py-2 sm:py-2 lg:py-2.5 xl:py-2.5"
        buttonMargin="my-5"
        buttonBg="bg-primary dark:bg-dark_primary"
        onHover="bg-black"
        buttonTextColor="text-white"
        hasHeading={true}
        heading={
          <BannerHeading
            largeHeading="true"
            largeHeadingText={t('home:sale_50')}
            largeHeadingColor="text-white"
            mediumHeading="true"
            mediumHeadingText={t('home:all_fitness')}
            mediumHeadingColor="text-white"
            smallHeading="true"
            smallHeadingText={t('home:black_fridays')}
            smallHeadingColor="text-primary dark:text-dark_primary"
          />
        }
      />
    </div>
  );
};

export default HomefullBanner;
