import Link from 'next/link';
import React from 'react';

import { trimDescription } from 'helper/trim';
import { UserProduct } from 'models';
import { useAppSelector } from 'store/hooks/index';

interface SingleProduct {
  product: UserProduct;
}

const ProductInfo: React.FC<SingleProduct> = (props: SingleProduct) => {
  const { product } = props;
  const currency = useAppSelector((state) => state.persistedReducer.currency);

  return (
    <div className="py-4 text-center">
      <Link prefetch={false} href="/product/1" passHref>
        <div className="text-base text-inherit text-gray-600">
          {trimDescription(product?.info.name, 15)}
          {product?.info?.stock! <= 0 && (
            <span className="text-xs text-dark_text">(Out of Stock)</span>
          )}
        </div>
      </Link>
      <p className="m-1 font-['arial'] text-sm text-gray-600 dark:text-dark_text">
        {product?.tags
          ? product?.tags[0]
          : product?.brands
          ? product?.brands[0]
          : ''}
      </p>
      <div className="text-lg font-semibold text-primary dark:text-dark_primary">
        {Intl.NumberFormat(
          `${currency.currencyLanguage}-${currency.currencyStyle}`,
          { style: 'currency', currency: `${currency.currencyName}` }
        ).format(product?.info?.price)}
        {/* {/* {product?.info?.price} */}
        {product?.info.oldPrice > product?.info.price ? (
          <span className="ml-2 text-xs font-semibold text-dark_bg dark:text-dark_text">
            <s>
              {Intl.NumberFormat(
                `${currency.currencyLanguage}-${currency.currencyStyle}`,
                { style: 'currency', currency: `${currency.currencyName}` }
              ).format(product?.info?.oldPrice)}
            </s>
          </span>
        ) : null}
      </div>
    </div>
  );
};

export default ProductInfo;
