import React from 'react';
import useTranslation from 'next-translate/useTranslation';

import { useRouter } from 'next/router';
import { useAppDispatch } from 'store/hooks/index';
import { setLoginModalState } from 'store/slices/modalSlice';

interface Props {
  setModalOn: Function;
  setChoice: Function;
  trigger?: Function;
  modalTitle?: string;
  bodyText?: string;
}
 


const Modal: React.FC<Props> = ({
  setModalOn,
  setChoice,
  trigger,
  modalTitle,
  bodyText,
}: Props) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();

  const handleOKClick = () => {
    setChoice(true);
    if (trigger) {
      setModalOn(false);
      dispatch(setLoginModalState(false));
      trigger();
    } else {
      setModalOn(false);
      dispatch(setLoginModalState(false));
      router.push('/account/sign-in');
    }
  };
  const handleCancelClick = () => {
    setChoice(false);
    dispatch(setLoginModalState(false));
    setModalOn(false);
  };

  return (
    <>
      <div
        className="fixed inset-0 z-50 bg-neutral-900/40"
        onClick={handleCancelClick}
      >
        <div className="flex h-screen items-center justify-around ">
          <div
            className="w-2/3 bg-white px-5 pt-5 dark:bg-zinc-700 sm:w-auto"
            onClick={(e: React.MouseEvent<HTMLDivElement, MouseEvent>) =>
              e.stopPropagation()
            }
          >
            <p>{modalTitle}</p>
            <hr className="mt-3" />
            <div className="flex-col items-center pt-5 pb-12 sm:px-24">
              <div className="mb-2">{bodyText}</div>
              <div className="flex flex-wrap gap-x-5 text-sm">
                <button
                  onClick={handleOKClick}
                  className="rounded bg-[#eef0f1] px-6 py-2 text-black hover:bg-primary hover:text-white  dark:hover:bg-dark_primary sm:px-10"
                >
                  {t('common:yes').toUpperCase()}
                </button>
                <button
                  onClick={handleCancelClick}
                  className="rounded bg-[#eef0f1] px-4 py-2 text-black transition-all duration-500 ease-in-out hover:bg-primary hover:text-white dark:hover:bg-dark_primary sm:px-8"
                >
                  {t('common:cancel').toUpperCase()}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Modal;
