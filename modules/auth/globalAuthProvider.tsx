import React, { useEffect, useState, useCallback } from 'react';
import { useAppSelector, useAppDispatch } from 'store/hooks/index';
import { useSession } from 'next-auth/react';
import axios from 'axios';
import { storeUserToken } from 'store/slices/authSlice';
import { storeProvider } from 'store/slices/providerSlice';
import { storeCustomerDetails } from 'store/slices/userSlice';
import { storeAddresses } from 'store/slices/customerAddressSlice';
import { userAPI } from 'APIs';
import { toast } from 'react-toastify';

const GlobalAuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const dispatch = useAppDispatch();
  const { data: session, status } = useSession();
  const [sessionHandled, setSessionHandled] = useState(false);
  const isLoggedIn = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );
  const providerName = useAppSelector(
    (state) => state?.persistedReducer?.provider?.provider
  );

  const handleSession = useCallback(async () => {
    if (sessionHandled) return; // Prevent multiple calls

    console.log('🔍 GlobalAuthProvider: Handling session...');
    setSessionHandled(true);

    try {
      // Check if we have a backend token from NextAuth session
      const backendToken = (session as any)?.backendToken;
      const backendUser = (session as any)?.backendUser;

      console.log(
        '🎫 Backend token from session:',
        backendToken ? 'Found' : 'Not found'
      );
      console.log(
        '👤 Backend user from session:',
        backendUser ? 'Found' : 'Not found'
      );

      if (backendToken) {
        // We have a backend token from NextAuth callback, use it directly
        console.log('✅ Using backend token from NextAuth session');
        dispatch(storeUserToken(backendToken));

        // Store provider information
        const provider = (session as any)?.account?.provider || 'google';
        dispatch(storeProvider(provider));

        // Fetch user data using the backend token
        const userResponse = await userAPI.getCustomer(backendToken);
        if ('data' in userResponse) {
          dispatch(storeCustomerDetails(userResponse.data));
          dispatch(storeAddresses(userResponse.data.addresses || []));
        }

        toast.success('Logged in successfully!', {
          containerId: 'bottom-right',
        });
        return;
      }

      // Fallback: If no backend token in session, try the old method
      console.log('🔄 No backend token in session, trying fallback method...');

      const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:4003';
      const accessToken = (
        await axios.get(`${frontendUrl}/api/auth/tokenHandler`)
      ).data.token;
      console.log(
        '🎫 Access token from tokenHandler:',
        accessToken ? 'Found' : 'Not found'
      );

      // Determine provider from session if not in Redux
      let sessionProvider = 'google';
      if (session?.user?.email) {
        sessionProvider = 'google';
      } else {
        sessionProvider = 'facebook';
      }

      const actualProvider = providerName || sessionProvider;
      console.log('🏪 Using provider:', actualProvider);

      const url =
        actualProvider === 'google'
          ? '/api/googlesignin'
          : '/api/facebooksignin';

      const tokenfromBE = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(accessToken),
      });

      const res = await tokenfromBE.json();
      console.log('🔄 Backend response:', res);
      console.log('🔄 Response status:', tokenfromBE.status);

      // Handle successful authentication (accept both 200 and 201)
      if (
        (tokenfromBE.status === 200 || tokenfromBE.status === 201) &&
        'data' in res &&
        res.data?.token
      ) {
        console.log('✅ Storing token in Redux...');
        dispatch(storeUserToken(res.data.token));

        // Fetch user data
        const userResponse = await userAPI.getCustomer(res.data.token);
        if ('data' in userResponse) {
          dispatch(storeCustomerDetails(userResponse.data));
          dispatch(storeAddresses(userResponse.data.addresses || []));
        }

        toast.success('Logged in successfully!', {
          containerId: 'bottom-right',
        });
        return;
      }

      // Handle authentication failures
      console.log('❌ Backend authentication failed');

      // Use session manager for proper cleanup
      const { sessionManager } = await import('../../utils/sessionManager');
      await sessionManager.invalidateSession({
        reason: 'unauthorized',
        showToast: true,
        redirectTo: '/account/sign-in'
      });
    } catch (error) {
      console.error('💥 Session handling error:', error);

      // Use session manager for proper cleanup
      const { sessionManager } = await import('../../utils/sessionManager');
      await sessionManager.invalidateSession({
        reason: 'session_expired',
        showToast: true,
        redirectTo: '/account/sign-in'
      });
    }
  }, [sessionHandled, session, providerName, dispatch]);

  useEffect(() => {
    console.log('🔐 GlobalAuthProvider check:', {
      status,
      session: !!session,
      isLoggedIn: !!isLoggedIn,
      sessionHandled,
    });

    // If NextAuth is still loading, wait
    if (status === 'loading') {
      return;
    }

    // If we have a token, we're authenticated - no need to handle session again
    if (isLoggedIn) {
      console.log('✅ User is authenticated, skipping session handling');
      return;
    }

    // If we have a session but no Redux token, handle the session
    if (session && !isLoggedIn && !sessionHandled) {
      console.log('🔄 Session found but no Redux token, handling session...');
      handleSession();
      return;
    }

    // If no session and no token, just mark as handled
    if (!session && !isLoggedIn && !sessionHandled) {
      console.log('🚫 No session and no token, marking as handled');
      setSessionHandled(true);
      return;
    }
  }, [session, isLoggedIn, status, sessionHandled, handleSession]);

  return <>{children}</>;
};

export default GlobalAuthProvider;
