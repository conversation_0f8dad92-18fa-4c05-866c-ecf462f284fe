import { accordionBody } from 'APIs/utils/types';
import { config } from 'config';
import Breadcrumb from '../common/breadcrumbs/breadcrumb';

const RefundComponent = () => {
  const accordionList: accordionBody[] = [
    {
      id: '1',
      title: 'Refunds (if applicable)',
      body: 'Once your return is received and inspected, we will send you an email to notify you that we have received your returned item. We will also notify you of the approval or rejection of your refund. If you are approved, then your refund will be processed, and a credit will automatically be applied to your credit card or original method of payment, within a certain amount of days.',
    },
    {
      id: '2',
      title: 'Late or missing refunds (if applicable)',
      body: `If you haven’t received a refund yet, first check your bank account again.
      Then contact your credit card company, it may take some time before your refund is officially posted.
      Next contact your bank. There is often some processing time before a refund is posted.
      If you’ve done all of this and you still have not received your refund yet, please contact us at ${config.email}`,
    },
    {
      id: '3',
      title: 'Sale items (if applicable)',
      body: 'Only regular priced items may be refunded, unfortunately sale items cannot be refunded.',
    },
    {
      id: '4',
      title: 'Exchanges (if applicable)',
      body: `We only replace items if they are defective or damaged. If you need to exchange it for the same item, send us an email at ${config.email} and send your item to our warehouse.`,
    },
    {
      id: '5',
      title: 'Shipping',
      body: 'You will be responsible for paying for your own shipping costs for returning your item. Shipping costs are non-refundable. If you receive a refund, the cost of return shipping will be deducted from your refund. Depending on where you live, the time it may take for your exchanged product to reach you, may vary.',
    },
  ];

  const sendEmail = () => {
    window.open(`mailto:<EMAIL>`);
  };

  return (
    <>
      <Breadcrumb
        title="Return Policy"
        pathArray={['Home', 'Return Policy']}
        linkArray={['/', '/return-policy']}
      />
      <div className="container mx-auto p-4">
        <div className="mx-0 flex flex-col items-center justify-center lg:mx-52">
          <h1 className="mt-10 text-xl font-bold lg:text-5xl">Return Policy</h1>
          <p className="mx-3 mt-5 text-justify">
            We want you to be completely satisfied with your purchase from our
            clothing store. However, if you are not satisfied with your order
            for any reason, we offer a full refund of the purchase price,
            provided the following conditions are met:
            <br />
            <br />
            <ul className="ml-10 list-disc">
              <li>
                The item must be returned in its original condition, with tags
                attached and in its original packaging.
              </li>
              <li>
                The return must be initiated within 30 days of receiving the
                item.
              </li>
              <li>
                The return must be accompanied by proof of purchase, such as a
                receipt or order confirmation.
              </li>
            </ul>
          </p>
          <p className="mx-3 mt-5 text-justify">
            To initiate a return, please contact our customer service team at
            <span className="cursor-pointer text-primary" onClick={sendEmail}>
              {' '}
              <EMAIL>
            </span>{' '}
            and provide your order number and the reason for the return. Our
            team will provide you with instructions on how to return the item.
          </p>
          <p className="mx-3 mt-5 text-justify">
            Once we receive the returned item and confirm that it meets the
            above conditions, we will issue a refund to the original payment
            method within 7-10 business days. Please note that shipping charges
            are non-refundable.
          </p>
          <p className="mx-3 mt-5 text-justify">
            If you received a defective or damaged item, please contact our
            customer service team immediately and provide a photo of the defect
            or damage. We will work with you to provide a replacement item or
            issue a full refund, including shipping charges.
          </p>
          <p className="mx-3 mt-5 text-justify">
            Please note that this refund policy only applies to purchases made
            directly from our clothing store. If you purchased our products from
            a third-party retailer, please refer to their refund policy.
          </p>
          <p className="mx-3 mt-5 text-justify">
            If you have any questions about our refund policy or the status of
            your refund, please contact our customer service team at{' '}
            <span className="cursor-pointer text-primary" onClick={sendEmail}>
              {' '}
              <EMAIL>
            </span>{' '}
            .
          </p>
          <br />
          {/* <RefundContentArea accordionList={accordionList} /> */}
        </div>
      </div>
    </>
  );
};

export default RefundComponent;
