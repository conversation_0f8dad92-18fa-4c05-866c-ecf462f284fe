import { FC } from 'react';
import { UserProduct } from 'models';
import VerticalProduct from '@/modules/common/product/verticalProduct';
import { config } from 'config';

const ProductList: FC<{ products: UserProduct[] }> = ({ products }) => {
  return (
    <>
      {/* {console.log(currentPage)} */}
      <div className="relative py-5">
        {config.showProduct === false ? (
          <div className="absolute inset-0 z-50 bg-neutral-50/70">
            <div className="flex h-screen items-center justify-around ">
              <div
                className="font-base  w-2/3 p-5 text-xl sm:w-auto"
                onClick={(e: React.MouseEvent<HTMLDivElement, MouseEvent>) =>
                  e.stopPropagation()
                }
              >
                Coming Soon...
              </div>
            </div>
          </div>
        ) : (
          ''
        )}
        {products && products[0] ? (
          <div className="grid grid-cols-2 justify-items-center gap-2 md:w-fit lg:grid-cols-3 lg:gap-[25px] xl:grid-cols-3 xl:gap-[25px]">
            {products.map((product) => (
              <VerticalProduct key={product.id} product={product} />
            ))}
          </div>
        ) : (
          <p className="m-auto text-center">
            There is no product in this category
          </p>
        )}
      </div>
    </>
  );
};

export default ProductList;
