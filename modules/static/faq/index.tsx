import { accordionBody } from 'APIs/utils/types';

import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import FaqContentArea from '@/modules/static/faq/components/contentArea';
import useTranslation from 'next-translate/useTranslation';

const accordionList: accordionBody[] = [
  {
    id: '507f1f77bcf86cd799439014',
    title:
      'Donec mattis finibus elit ut tristique?Aenean elit orci, efficitur quis nisl at, accumsan?',
    body: 'Donec mattis finibus elit ut tristique. Nullam tempus nunc eget arcu vulputate, eu porttitor tellus commodo. Aliquam erat volutpat. Aliquam consectetur lorem eu viverra lobortis. Morbi gravida, nisi id fringilla ultricies, elit lorem eleifend lorem Donec mattis finibus elit ut tristique?A<PERSON>an elit orci, efficitur quis nisl at, accumsan?Pellentesque habitant morbi tristique senectus et netus?Nam pellentesque aliquam metus?<PERSON><PERSON><PERSON> elit orci, efficitur quis nisl at?Morbi gravida, nisi id fringilla ultricies, elit lorem?Aenean elit orci, efficitur quis nisl at, accumsan',
  },
  {
    id: '507f1f77bcf86cd799439015',
    title: 'More Than 30 Years In The Business',
    body: 'Nam liber tempor cum soluta nobis eleifend option. Congue nihilimperdiet doming id quod mazim placerat facer possim assum. Typi nonhabent claritatem insitam est usus legentis in iis qui facit eorumclaritatem. Investigationes demonstraverunt lectores legere me.Claritas est etiam processus dynamicus, qui sequitur mutationem consuetudium lectorum.',
  },
  {
    id: '507f1f77bcf86cd799439016',
    title: '100% Organic Foods',
    body: 'Nam liber tempor cum soluta nobis eleifend option. Congue nihilimperdiet doming id quod mazim placerat facer possim assum. Typi nonhabent claritatem insitam est usus legentis in iis qui facit eorumclaritatem. Investigationes demonstraverunt lectores legere me.Claritas est etiam processus dynamicus, qui sequitur mutationemconsuetudium lectorum.',
  },
  {
    id: '507f1f77bcf86cd799439017',
    title:
      'Donec mattis finibus elit ut tristique?Aenean elit orci, efficitur quis nisl at, accumsan?',
    body: 'Nam liber tempor cum soluta nobis eleifend option. Congue nihilimperdiet doming id quod mazim placerat facer possim assum. Typi nonhabent claritatem insitam est usus legentis in iis qui facit eorumclaritatem. Investigationes demonstraverunt lectores legere me.Claritas est etiam processus dynamicus, qui sequitur mutationemconsuetudium lectorum.',
  },
  {
    id: '507f1f77bcf86cd799439018',
    title: '100% Organic Foods',
    body: 'Nam liber tempor cum soluta nobis eleifend option. Congue nihilimperdiet doming id quod mazim placerat facer possim assum. Typi nonhabent claritatem insitam est usus legentis in iis qui facit eorumclaritatem. Investigationes demonstraverunt lectores legere me.Claritas est etiam processus dynamicus, qui sequitur mutationemconsuetudium lectorum.',
  },
  {
    id: '507f1f77bcf86cd799439019',
    title: 'Donec mattis finibus elit ut tristique?Aenean elit orci?',
    body: 'Nam liber tempor cum soluta nobis eleifend option. Congue nihilimperdiet doming id quod mazim placerat facer possim assum. Typi nonhabent claritatem insitam est usus legentis in iis qui facit eorumclaritatem. Investigationes demonstraverunt lectores legere me.Claritas est etiam processus dynamicus, qui sequitur mutationemconsuetudium lectorum.',
  },
  {
    id: '507f1f77bcf86cd799439020',
    title: 'Nam liber tempor cum soluta nobis eleifend option?',
    body: 'Nam liber tempor cum soluta nobis eleifend option. Congue nihilimperdiet doming id quod mazim placerat facer possim assum. Typi nonhabent claritatem insitam est usus legentis in iis qui facit eorumclaritatem. Investigationes demonstraverunt lectores legere me.Claritas est etiam processus dynamicus, qui sequitur mutationemconsuetudium lectorum.',
  },
  {
    id: '507f1f77bcf86cd799439021',
    title: '100% Organic Foods',
    body: ' Nam liber tempor cum soluta nobis eleifend option. Congue nihilimperdiet doming id quod mazim placerat facer possim assum. Typi nonhabent claritatem insitam est usus legentis in iis qui facit eorumclaritatem. Investigationes demonstraverunt lectores legere me.Claritas est etiam processus dynamicus, qui sequitur mutationemconsuetudium lectorum.',
  },
];

const FaqComponent: React.FC = () => {
  const { t } = useTranslation();
  return (
    <>
      <Breadcrumb
        title={t('common:faq')}
        pathArray={[`${t('common:home')}`, `${t('common:faq')} `]}
        linkArray={['/', '/']}
      />
      <div className="mt-10">
        <FaqContentArea accordionList={accordionList} />
      </div>
    </>
  );
};

export default FaqComponent;
