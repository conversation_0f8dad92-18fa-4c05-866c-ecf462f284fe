import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { FC } from 'react';
import ImageWithDescription from '@/modules/static/clubDisclaimer/components/imageWithDescription';

const ClubDisclaimerComponent: FC = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const prev = router.query.prev;
  return (
    <>
      <Breadcrumb
        title="Disclaimer"
        pathArray={[`${t('common:home')}`, `Disclaimer`]}
        linkArray={[prev ? '/' : '/', '/']}
      />
      <div className="py-12">
        <ImageWithDescription />
      </div>
    </>
  );
};

export default ClubDisclaimerComponent;
