import myI<PERSON><PERSON>oader from 'image/loader';
import Image from 'next/image';
import { FC } from 'react';

const ImageWithDescription: FC = () => {
  return (
    <>
      <div className="container mx-auto flex flex-col items-center justify-center gap-10 px-4">
        <Image
          loader={myImageLoader}
          src="/friend.svg"
          width={700}
          height={400}
          alt="about"
        />
        <div>
          <p className="mx-auto max-w-4xl text-justify text-sm">
            This fitness app features a &quot;Club&quot; section that includes a
            database of gym names, allowing users to virtually join and switch
            between different gyms. It is important to note that the inclusion
            of these gym names within the app does not indicate endorsement,
            affiliation, or authorization from the respective gym owners or
            authorized sources unless explicitly stated.
          </p>
          <br />
          <p className="mx-auto max-w-4xl text-justify text-sm">
            The gym names listed in this app are sourced from publicly available
            directories and other publicly accessible information. While efforts
            have been made to ensure the accuracy and timeliness of the
            information, we cannot guarantee its completeness or reliability.
          </p>
          <br />
          <p className="mx-auto max-w-4xl text-justify text-sm">
            Please be aware that the virtual joining and switching of gyms
            within this app does not grant users actual membership or access to
            these gyms in real life. The &quot;Club&quot; feature is intended
            for entertainment and interactive purposes only and should not be
            construed as an official representation of any gym or membership
            status.
          </p>
          <br />
          <p className="mx-auto max-w-4xl text-justify text-sm">
            Additionally, this app includes a sub-feature within the Clubs
            feature that allows users to view each other&apos;s live location
            and personal information. It is important to respect the privacy of
            all users. The sharing of personal information and live location
            within the app is solely at the discretion and responsibility of the
            users involved. We strongly encourage users to exercise caution and
            discretion when sharing personal information or live location data.
          </p>
          <br />
          <p className="mx-auto max-w-4xl text-justify text-sm">
            By using this app and participating in the &quot;Club&quot; feature
            or utilizing the sub-feature for sharing personal information and
            live location, you acknowledge and agree that the app developer, its
            affiliates, and partners shall not be held responsible or liable for
            any inaccuracies, omissions, legal implications, privacy concerns,
            or discrepancies that may arise from the use of gym names or
            personal information within this app.
          </p>
          <br />
          <p className="mx-auto max-w-4xl text-justify text-sm">
            We advise users to carefully consider the implications of sharing
            personal information and live location data, and to consult with
            legal professionals regarding privacy and data protection laws,
            intellectual property rights, and any other legal considerations
            before using the app&apos;s features
          </p>
        </div>
        {/* <Image
        loader={myImageLoader}
        src="https://cdn.shopify.com/s/files/1/0359/6350/2651/files/about-us-signature_medium.png?v=1588134272"
        width={228}
        height={129}
        alt="about"
      /> */}
      </div>
    </>
  );
};

export default ImageWithDescription;
