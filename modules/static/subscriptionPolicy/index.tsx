import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';

const SubscriptionPolicyComponent = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const prev = router.query.prev;

  return (
    <>
      <div className="container mx-auto flex flex-col items-center justify-center gap-10">
        <div>
          <p className="font-semibold">SUBSCRIPTION POLICY</p>

          <p className="mx-auto max-w-4xl text-justify">
            Subscriptions allow Users to receive a Product continuously or
            regularly over time. Details regarding the type of subscription and
            termination are outlined below.
          </p>
          <br />
          <p className="mx-auto max-w-4xl text-justify">
            At Fitsomnia, we strive to provide our users with the best possible
            fitness experience. As part of our commitment to transparency and
            ensuring a clear understanding of our subscription services, we have
            established the following Subscription Policy for our app:
          </p>
          <br />
          <p className="font-semibold">Subscription Options: </p>
          <p className="mx-auto max-w-4xl text-justify">
            <ul className="ml-5 list-disc md:ml-10">
              <li>
                {' '}
                Fitsomnia offers a variety of subscription options to meet the
                diverse needs of our users. These subscriptions provide access
                to premium features and exclusive content within the app. The
                available subscription plans and their respective benefits are
                clearly presented in the app&apos;s subscription section.
              </li>
            </ul>
          </p>
          <br />
          <p className="font-semibold">Subscription Pricing: </p>
          <p className="mx-auto max-w-4xl text-justify">
            <ul className="ml-5 list-disc md:ml-10">
              <li>
                {' '}
                The pricing for Fitsomnia subscriptions is clearly communicated
                to users before they confirm their purchase. We follow a
                transparent pricing policy, ensuring that users are fully
                informed about the cost of each subscription plan.
              </li>
            </ul>
          </p>
          <br />

          <p className="font-semibold">Subscription Duration and Renewal:</p>
          <p className="mx-auto max-w-4xl text-justify">
            <ul className="ml-5 list-disc md:ml-10">
              <li>
                {' '}
                Fitsomnia offers subscriptions with monthly and annual
                durations. The duration of your subscription is determined by
                the plan you select during the purchase process. Monthly
                subscriptions will renew automatically on a monthly basis, while
                annual subscriptions will renew annually.
              </li>
            </ul>
          </p>
          <br />

          <p className="font-semibold">Free Trial Period:</p>
          <p className="mx-auto max-w-4xl text-justify">
            <ul className="ml-5 list-disc md:ml-10">
              <li>
                {' '}
                We understand the importance of allowing users to explore and
                experience our app before committing to a subscription.
                Fitsomnia may offer a free trial period for new users. The
                duration of the free trial and any conditions associated with it
                are communicated clearly to users prior to initiating the trial.
              </li>
            </ul>
          </p>
          <br />

          <p className="font-semibold">Payment and Billing:</p>
          <p className="font-semibold">For IOS:</p>
          <p className="mx-auto max-w-4xl text-justify">
            <ul className="ml-5 list-disc md:ml-10">
              <li>
                {' '}
                Fitsomnia utilizes the standard payment mechanisms provided by
                the App Store. Users are billed through their Apple ID accounts
                according to the chosen subscription plan and the designated
                billing cycle (monthly or annually).
              </li>
            </ul>
          </p>
          <br />

          <p className="font-semibold">For Android:</p>
          <p className="mx-auto max-w-4xl text-justify">
            <ul className="ml-5 list-disc md:ml-10">
              <li>
                {' '}
                Fitsomnia utilizes the standard payment mechanisms provided by
                the Google Play Store. Users are billed through their Google
                Play Store accounts according to the chosen subscription plan
                and the designated billing cycle (monthly or annually).
              </li>
            </ul>
            Payment details and billing information are managed directly through
            the App Store & Google Play Store, ensuring a secure and reliable
            payment process.
          </p>
          <br />

          <p className="font-semibold">
            Subscription Management and Cancellation:
          </p>
          <p className="mx-auto max-w-4xl text-justify">
            <ul className="ml-5 list-disc md:ml-10">
              <li>
                {' '}
                Users have full control over their Fitsomnia subscriptions and
                can manage them through their Google Play Store (for android
                users), App Store (for IOS users) account settings. If a user
                wishes to cancel their subscription or make any changes to their
                subscription plan, they can do so directly through the Google
                Play Store or App Store based on their purchase. We ensure that
                the cancellation process is straightforward and user-friendly.
              </li>
            </ul>
          </p>
          <br />

          <p className="font-semibold">Changes to Subscription Policy:</p>
          <p className="mx-auto max-w-4xl text-justify">
            <ul className="ml-5 list-disc md:ml-10">
              <li>
                {' '}
                Fitsomnia reserves the right to modify or update the
                Subscription Policy as necessary. Any changes to the policy will
                be communicated to users through the app&apos;s update
                notifications or via other appropriate channels. It is advisable
                for users to review the policy periodically to stay informed
                about any revisions.
              </li>
            </ul>
          </p>
          <br />

          <p className="font-semibold">Subscriptions handled via Apple ID:</p>
          <p className="mx-auto max-w-4xl text-justify">
            Users may subscribe to a Product using the Apple ID associated with
            their Apple App Store account by using the relevant process on this
            Application. When doing so, Users acknowledge and accept that
            <br />
            <ul className="ml-5 list-disc md:ml-10">
              <li>
                {' '}
                any payment due shall be charged to their Apple ID account;
              </li>
              <li>
                {' '}
                subscriptions are automatically renewed for the same duration
                unless the User cancels at least 24 hours before the current
                period expires;
              </li>
              <li>
                {' '}
                any and all fees or payments due for renewal will be charged
                within 24-hours before the end of the current period;
              </li>
              <li>
                {' '}
                subscriptions can be managed or cancelled in the Users’ Apple
                App Store account settings.
              </li>
            </ul>
          </p>
          <br />
          <p className="mx-auto max-w-4xl text-justify">
            {' '}
            The above shall prevail upon any conflicting or diverging provision
            of these Terms.
            <br />
            By using Fitsomnia and subscribing to our services, users agree to
            adhere to the terms and conditions outlined in this Subscription
            Policy.
            <br />
            If you have any questions or need further assistance regarding your
            Fitsomnia subscription, please contact our support team at
            <span
              className="ml-2 cursor-pointer text-primary"
              onClick={() => {
                window.open(`mailto:<EMAIL>`);
              }}
            >
              <EMAIL>
            </span>
          </p>
        </div>
      </div>
    </>
  );
};

export default SubscriptionPolicyComponent;
