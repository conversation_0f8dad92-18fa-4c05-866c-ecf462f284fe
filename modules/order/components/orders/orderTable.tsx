import TableData from '@/modules/order/components/orders/tableData';
import { OrderByUserId } from 'models';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface Props {
  orderList: OrderByUserId[];
  getAllOrders: Function;
}
const OrderTable: React.FC<Props> = ({ orderList, getAllOrders }) => {
  const { t } = useTranslation();

  return (
    <>
      <div className="mt-5">
        <div className="overflow-x-auto rounded-lg border">
          <div className="inline-block min-w-full py-2 sm:px-4">
            <table className="inline-table w-full text-left text-sm">
              <thead className="">
                <tr className="border-b text-center">
                  <th scope="col" className="px-5 py-4">
                    Order IDs
                  </th>
                  <th scope="col" className="px-5 py-4">
                    {t('order:date')}
                  </th>
                  <th scope="col" className="px-5 py-4">
                    {t('order:status')}
                  </th>
                  <th scope="col" className="px-5 py-4">
                    {t('order:payment_method')}
                  </th>
                  <th scope="col" className="px-5 py-4">
                    Payment Status
                  </th>
                  <th scope="col" className="px-5 py-4">
                    Total Cost <br />
                    (Product cost + Shipping charge)
                  </th>
                  <th scope="col" className="px-5 py-4">
                    {t('order:action')}
                  </th>
                </tr>
              </thead>
              <tbody>
                {orderList?.map((singleOrder, index) => {
                  return (
                    <React.Fragment key={singleOrder?.orderId}>
                      <tr
                        className={
                          index === orderList?.length - 1
                            ? 'border-none text-center'
                            : 'border-b text-center'
                        }
                      >
                        <TableData
                          singleOrder={singleOrder}
                          getAllOrders={getAllOrders}
                        />
                      </tr>
                    </React.Fragment>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
};

export default OrderTable;
