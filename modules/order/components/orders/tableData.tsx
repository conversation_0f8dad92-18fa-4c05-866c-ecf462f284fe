import Modal from '@/modules/common/modal';
import { userAPI } from 'APIs';
import { OrderByUserId, PaymentMethodEnum, PaymentStatusEnum } from 'models';
import moment from 'moment';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { useAppSelector } from 'store/hooks/index';

interface Props {
  singleOrder: OrderByUserId;
  getAllOrders: Function;
}
const TableData: React.FC<Props> = ({ singleOrder, getAllOrders }) => {
  const { t } = useTranslation();
  const currency = useAppSelector((state) => state.persistedReducer.currency);

  const [modalOn, setModalOn] = useState(false);
  const [choice, setChoice] = useState(false);

  const [cancelModalOn, setCancelModalOn] = useState(false);
  const [cancelChoice, setCancelChoice] = useState(false);

  const handlePayNow = async () => {
    try {
      if (singleOrder?.paymentMethod === PaymentMethodEnum.Stripe) {
        const res = await userAPI.stripeRepay(singleOrder?.orderId);
        if ('data' in res) {
          window.open(res.data);
        } else {
          toast.error(res?.error.message, {
            containerId: 'bottom-right',
          });
        }
      }
    } catch (error) {}
  };

  const handleOrderCancel = async () => {
    try {
      const res = await userAPI.cancelOrder(singleOrder?.orderId!);
      if ('data' in res) {
        toast.success('Order cancelled successfully', {
          containerId: 'bottom-right',
        });
        getAllOrders();
      } else {
        toast.success(res?.error?.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  return (
    <>
      {modalOn && (
        <Modal
          setModalOn={setModalOn}
          setChoice={setChoice}
          trigger={handlePayNow}
          modalTitle={`Confirm Redirect`}
          bodyText={`${t('common:are_you_sure')}`}
        />
      )}

      {cancelModalOn && (
        <Modal
          setModalOn={setCancelModalOn}
          setChoice={setCancelChoice}
          trigger={handleOrderCancel}
          modalTitle={`Cancel Order`}
          bodyText={`${t('common:are_you_sure')}`}
        />
      )}

      {/* {cancelModalOn && (
        <Modal
          setModalOn={setCancelChoice}
          setChoice={setCancelChoice}
          trigger={handleOrderCancel}
          modalTitle={`Cancel Order`}
          bodyText={`${t('common:are_you_sure')}`}
        />
      )} */}
      <td className="px-5 py-4">{singleOrder?.orderId}</td>
      <td className="px-5 py-4">
        {moment(singleOrder?.orderedDate).format('ll')}
      </td>
      <td className="px-5 py-4">
        {singleOrder?.orderStatus.toLowerCase() === 'pending' && (
          <button className="cursor-text rounded-md bg-[#fcd34d] bg-opacity-20 p-1 dark:bg-teal-500 dark:text-black">
            Pending
          </button>
        )}
        {singleOrder?.orderStatus.toLowerCase() === 'completed' && (
          <button className="cursor-text rounded-md bg-[#1d8a51] bg-opacity-10 p-1 dark:bg-green-600 dark:text-white">
            Completed
          </button>
        )}
        {singleOrder?.orderStatus.toLowerCase() === 'processing' && (
          <button className="cursor-text rounded-md bg-[#a9b31e] bg-opacity-10 p-1 dark:bg-orange-600 dark:text-white">
            Processing
          </button>
        )}
        {singleOrder?.orderStatus.toLowerCase() === 'cancelled' && (
          <button className="cursor-text rounded-md bg-[#ff0000] bg-opacity-20 p-1 dark:bg-red-800 dark:text-white">
            Cancelled
          </button>
        )}
      </td>
      <td className="px-5 py-4">{singleOrder?.paymentMethod}</td>
      <td className="px-5 py-4">
        <div className="flex items-center justify-center gap-1">
          {singleOrder?.paymentStatus}
          {singleOrder?.paymentStatus === PaymentStatusEnum.Pending &&
            singleOrder?.paymentMethod === PaymentMethodEnum.Stripe && (
              <button
                onClick={() => setModalOn(true)}
                className="ml-0 cursor-pointer rounded-xl border bg-[#1d8a51] bg-opacity-10 p-1 text-xs  transition-all duration-500 ease-in-out hover:bg-gray-300 dark:bg-teal-500 dark:text-black hover:dark:bg-gray-300 hover:dark:text-black lg:ml-2"
              >
                Pay Now
              </button>
            )}
          {singleOrder?.paymentStatus === PaymentStatusEnum.Pending &&
            singleOrder?.paymentMethod === PaymentMethodEnum.Stripe && (
              <p>Or</p>
            )}
          {singleOrder?.paymentStatus === PaymentStatusEnum.Pending &&
            singleOrder?.paymentMethod === PaymentMethodEnum.Stripe && (
              <button
                onClick={() => setCancelModalOn(true)}
                className="ml-0 cursor-pointer rounded-xl border bg-[#ff0000] bg-opacity-10 p-1 text-xs  transition-all duration-500 ease-in-out hover:bg-gray-300 dark:bg-teal-500 dark:text-black hover:dark:bg-gray-300 hover:dark:text-black lg:ml-2"
              >
                Cancel Order
              </button>
            )}
        </div>
      </td>
      <td className="px-5 py-4">
        {Intl.NumberFormat(
          `${currency.currencyLanguage}-${currency.currencyStyle}`,
          { style: 'currency', currency: `${currency.currencyName}` }
        ).format(singleOrder?.totalCost)}
      </td>
      <td className="px-5 py-4 text-primary hover:text-black dark:text-dark_primary hover:dark:text-dark_text">
        <Link
          prefetch={false}
          href={{
            pathname: `/order/[id]`,
            query: { id: singleOrder?.orderId },
          }}
          legacyBehavior
        >
          {t('order:view')}
        </Link>
      </td>
    </>
  );
};
export default TableData;
