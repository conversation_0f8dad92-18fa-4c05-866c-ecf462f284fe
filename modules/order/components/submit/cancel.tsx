import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import Image from 'next/legacy/image';
import Link from 'next/link';
import { FC } from 'react';

import withAuth from '@/modules/auth/withAuth';
import myImageLoader from 'image/loader';
import useTranslation from 'next-translate/useTranslation';
const CancelComponent: FC = () => {
  const imageDimensions = { width: 400, height: 300 };
  const { t } = useTranslation();

  return (
    <>
      <Breadcrumb
        title="Payment Cancelled"
        pathArray={[`${t('common:home')}`, `Payment Cancelled`]}
        linkArray={['/', '/']}
      />
      <section className="container mx-auto px-4">
        <div className="flex flex-col items-center border-b py-16">
          <div>
            <Image
              loader={myImageLoader}
              src="/Transaction-Cancelled.png"
              alt="Page not found!!"
              width={imageDimensions.width}
              height={imageDimensions.height}
              className="mb-0"
            />
          </div>
          <span className="mb-2 font-bold ">
            Your Order has been placed. Please visit{' '}
            <span className="text-primary hover:text-black dark:text-dark_primary hover:dark:text-dark_text">
              <Link prefetch={false} href="/order">
                order list
              </Link>
            </span>{' '}
            to pay now.
          </span>
          <button className="rounded-md bg-primary py-2 px-6 font-light text-white transition-all duration-200 ease-linear hover:bg-stone-900 dark:bg-dark_primary">
            <Link prefetch={false} href="/order">
              Go to Order List
            </Link>
          </button>
        </div>
      </section>
    </>
  );
};

export default withAuth(CancelComponent);
