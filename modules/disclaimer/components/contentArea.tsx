import { FC } from 'react';

import Accordion from '@/modules/common/accordion';

import PageContainer from '@/modules/common/layout/pageContainer';
import { accordionBody } from 'APIs/utils/types';

interface Props {
  accordionList: accordionBody[];
}

const DisclaimerContentArea: FC<Props> = ({ accordionList }) => {
  return (
    <PageContainer>
      <div className="py-5">
        {accordionList?.map((item) => (
          <Accordion title={item.title} body={item.body} key={item.id} />
        ))}
      </div>
    </PageContainer>
  );
};

export default DisclaimerContentArea;
