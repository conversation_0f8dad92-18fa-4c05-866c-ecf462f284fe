{"name": "storefront", "version": "0.1.0", "private": true, "scripts": {"dev": "next -p 4003", "build": "next build", "start": "next start -p 4003", "lint": "tsc --noEmit && eslint .", "test": "jest --watch", "cypress": "cypress open", "check-types": "tsc --noEmit", "hygen": "hygen templates", "prepare": "husky install"}, "dependencies": {"@headlessui/react": "^1.6.4", "@popperjs/core": "^2.11.4", "@reduxjs/toolkit": "^1.8.6", "@storybook/react": "^6.5.13", "@types/react-redux": "^7.1.24", "autoprefixer": "^10.4.12", "axios": "^1.1.3", "escape-html": "^1.0.3", "font-awesome": "^4.7.0", "formik": "^2.2.9", "lodash": "^4.17.21", "moment": "^2.29.4", "next": "13.0.3", "next-auth": "^4.18.0", "next-themes": "^0.2.1", "next-translate": "^1.6.0", "next-transpile-modules": "^9.1.0", "react": "18.2.0", "react-cookie": "^4.1.1", "react-dom": "18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-lazyload": "^3.2.0", "react-loader-spinner": "^5.1.5", "react-multi-carousel": "^2.8.5", "react-redux": "^8.0.2", "react-spinners": "^0.13.2", "react-toastify": "^9.0.8", "redux-persist": "^6.0.0", "swiper": "^8.1.5", "ts-node": "^10.9.1", "xregexp": "^5.1.0", "yup": "^0.32.11"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@types/babel__core": "^7.1.19", "@types/jest": "^27.5.1", "@types/node": "17.0.22", "@types/react": "17.0.41", "@types/react-lazyload": "^3.2.0", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^28.1.0", "cypress": "^10.3.0", "eslint": "8.11.0", "eslint-config-next": "13.0.3", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "form-data": "^4.0.0", "husky": "^8.0.0", "hygen": "^6.2.11", "identity-obj-proxy": "^3.0.0", "jest": "^28.1.0", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "prettier-plugin-tailwindcss": "^0.1.11", "react-copy-to-clipboard": "^5.1.0", "react-test-renderer": "^18.1.0", "sass": "^1.49.9", "tailwindcss": "^3.0.24", "typescript": "4.6.2"}, "resolutions": {"@types/react": "17.0.2", "@types/react-dom": "17.0.2"}}