
# Fitsomnia Client Web

Fitsomnia client offers a diverse range of workout items and user friendly ecommerce experince . 



## Installation
Please follow the below instructions to install this project in your machine:

##### 1. Clone this repository -

```bash
$ git clone https://gitlab.com/fitsomania/client-web.git
```

##### 2. Go to the cloned project directory
```bash
$ cd client-web
```
###### 3. Install dependencies
```bash
$ npm install
```

## Set up environment

- Open the .env.example file, convert it to a .env.local file, and modify the configuration variables according to your needs. 


## Running the Application

#####  To start the apalication, run the following command:
```bash
# For Starting the project
$ npm run dev

# For production 
$ npm run build
````


A high level overview of Client Web Project Structure

```plaintext


└── client-web/
    ├── APIs
    ├── config
    ├── image
    ├── locales/
    │   └── bn/
    │       └── about.json
    ├── models/
    │   └── brand/
    │       └── brand.ts
    ├── modules/
    │   └── order/
    │       └── components/
    │           └── orders/
    │               └── index.tsx
    ├── pages/
    │   ├── order/
    │   │   └── [id].tsx
    │   └── _app.tsx
    ├── public
    ├── store/
    │   └── hooks/
    │       └── index.ts
    ├── styles
    ├── .env.local
    └── README.md
                     

```


A high level overview of pages structure

```plaintext
└── pages/
    ├── order/
    │   ├── [id].tsx
    │   └── index.tsx
    └── _app.tsx
 
```


A high level overview of modules structure

```plaintext
└── modules/
    └── components/
        ├── orders/
        │   └── index.tsx
        └── singleOrder/
            └── reOrder/
                ├── components/
                │   └── reorderModal/
                │       └── index.tsx
                └── index.tsx
 
```








