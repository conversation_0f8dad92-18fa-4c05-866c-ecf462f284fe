import { signOut } from "next-auth/react";
import { NextRouter } from "next/router";
import { toast } from "react-toastify";
import { storeUserToken } from "store/slices/authSlice";
import { resetCart } from "store/slices/cartSlice";
import { deleteCheckoutInfo } from "store/slices/checkoutSlice";
import { resetCompare } from "store/slices/compareSlice";
import { resetAddress } from "store/slices/customerAddressSlice";
import { resetWishilist } from "store/slices/productsSlice";
import { storeProvider } from "store/slices/providerSlice";
import { resetUserDetails } from "store/slices/userSlice";

export const handleLogout= (localStorage: Storage, dispatch: any, providerName: string, router: NextRouter,) => {
    localStorage.removeItem('persist:root');
    dispatch(resetAddress());
    dispatch(resetUserDetails());
    dispatch(resetWishilist());
    dispatch(resetCart());
    dispatch(resetCompare());
    dispatch(deleteCheckoutInfo());
    dispatch(storeUserToken(''));
    dispatch(storeProvider('none'));
    if (providerName !== 'none') {
      signOut({ callbackUrl: '/account/sign-in' });
    } else {
      router.push('/account/sign-in');
    }
    if(!router.query.clear) {
    toast.error('Logged out successfully!', {
      containerId: 'bottom-right',
    });
  }
  };