export function timeAgo(
  date: string | Date,
  numericDates: boolean = true
): string {
  const now = new Date();
  const difference = Math.floor(
    (now.getTime() - new Date(date).getTime()) / 1000
  ); // Difference in seconds

  const intervals: { [key: string]: number } = {
    year: 31536000,
    month: 2592000,
    week: 604800,
    day: 86400,
    hour: 3600,
    minute: 60,
    second: 1,
  };

  for (const [key, value] of Object.entries(intervals)) {
    const count = Math.floor(difference / value);
    if (count >= 2) {
      return `${count} ${key}s ago`;
    } else if (count === 1) {
      if (key === 'year') return numericDates ? '1 year ago' : 'Last year';
      if (key === 'month') return numericDates ? '1 month ago' : 'Last month';
      if (key === 'week') return numericDates ? '1 week ago' : 'Last week';
      if (key === 'day') return numericDates ? '1 day ago' : 'Yesterday';
      if (key === 'hour') return numericDates ? '1 hour ago' : 'An hour ago';
      if (key === 'minute')
        return numericDates ? '1 minute ago' : 'A minute ago';
      return `${count} ${key} ago`;
    }
  }
  return 'Just now';
}
