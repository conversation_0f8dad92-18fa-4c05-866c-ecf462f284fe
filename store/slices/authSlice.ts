import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface TokenState {
  access_token: string;
  isSessionValid: boolean;
  lastValidationTime: number | null;
}

const initialState: TokenState = {
  access_token: "",
  isSessionValid: false,
  lastValidationTime: null,
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    storeUserToken: (state: TokenState, action: PayloadAction<string>) => {
      state.access_token = action.payload;
      state.isSessionValid = !!action.payload;
      state.lastValidationTime = action.payload ? Date.now() : null;
    },
    updateSessionValidation: (state: TokenState, action: PayloadAction<{ isValid: boolean; timestamp?: number }>) => {
      state.isSessionValid = action.payload.isValid;
      state.lastValidationTime = action.payload.timestamp || Date.now();
    },
    clearAuthState: (state: TokenState) => {
      state.access_token = "";
      state.isSessionValid = false;
      state.lastValidationTime = null;
    },
  },
});

export const { storeUserToken, updateSessionValidation, clearAuthState } = authSlice.actions;

export default authSlice.reducer;
