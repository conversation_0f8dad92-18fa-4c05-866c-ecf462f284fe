import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User } from 'models';

export interface UserState {
  user: string;
  customerDetails: User;
}

const initialState: UserState = {
  user: '',
  customerDetails: {
    id: '',
    name: '',
    email: '',
    dateOfBirth: new Date(),
    maxSquat: 1
  },
};

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    storeUserDetails: (state: UserState, action: PayloadAction<string>) => {
      state.user = action.payload;
    },
    storeCustomerDetails: (
      state: UserState,
      action: PayloadAction<User>
    ) => {
      state.customerDetails = action.payload;
    },
    resetUserDetails: (state: UserState) => {
      state.user = initialState.user;
      state.customerDetails = initialState.customerDetails;
    },
  },
});

export const { storeUserDetails, storeCustomerDetails, resetUserDetails } =
  userSlice.actions;

export default userSlice.reducer;
