import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface ProviderState {
  provider: string;
}

const initialState: ProviderState = {
  provider: "none",
};

export const providerSlice = createSlice({
  name: "provider",
  initialState,
  reducers: {
    storeProvider: (state: ProviderState, action: PayloadAction<string>) => {
      state.provider = action.payload;
    },
  },
});

export const { storeProvider } = providerSlice.actions;

export default providerSlice.reducer;
