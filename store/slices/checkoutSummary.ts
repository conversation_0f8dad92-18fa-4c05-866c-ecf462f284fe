import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { TagType } from 'models';

export interface SummaryState {
  summary: {
    products: [
      {
        productId: string;
        sku: string;
        name: string;
        photo: string;
        price: number;
        isRedeemable: boolean;
        quantity: number;
        totalPrice: number;
      }
    ];
    billingAddress: {
      firstName: string;
      lastName: string;
      email: string;
      addressLine1: string;
      addressLine2?: string;
      city: string;
      postCode: string;
      phone: string;
      tag: TagType;
    };
    shippingAddress: {
      firstName: string;
      lastName: string;
      email: string;
      addressLine1: string;
      addressLine2?: string;
      city: string;
      postCode: string;
      phone: string;
      tag: TagType;
    };
    paymentMethod: string;
    productCost: number;
    shippingCost: number;
    totalCost: number;
  } | null;
}

const initialState: SummaryState = {
  summary: {
    products: [
      {
        productId: '',
        sku: '',
        name: '',
        photo: '',
        price: 0,
        isRedeemable: false,
        quantity: 0,
        totalPrice: 0,
      },
    ],
    billingAddress: {
      firstName: '',
      lastName: '',
      email: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      postCode: '',
      phone: '',
      tag: TagType.HOME,
    },
    shippingAddress: {
      firstName: '',
      lastName: '',
      email: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      postCode: '',
      phone: '',
      tag: TagType.HOME,
    },
    paymentMethod: '',
    productCost: 0,
    shippingCost: 0,
    totalCost: 0,
  },
};

export const summarySlice = createSlice({
  name: 'summary',
  initialState,
  reducers: {
    storeSummary: (state: SummaryState, action: PayloadAction<any>) => {
      state.summary = action.payload;
    },
    resetSummary: (state: SummaryState) => {
      state.summary = null;
    },
  },
});

export const { storeSummary, resetSummary } = summarySlice.actions;

export default summarySlice.reducer;
