import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { persistReducer, persistStore } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

import authReducer from 'store/slices/authSlice';
import cartReducer from 'store/slices/cartSlice';
import categoryReducer from 'store/slices/categorySlice';
import checkoutReducer from 'store/slices/checkoutSlice';
import checkoutSummaryReducer from 'store/slices/checkoutSummary';
import compareReducer from 'store/slices/compareSlice';
import currencyReducer from 'store/slices/currencySlice';
import UserAddressReducer from 'store/slices/customerAddressSlice';
import forgetPasswordReducer from 'store/slices/forgetPasswordSlice';
import modalReducer from 'store/slices/modalSlice';
import productsReducer from 'store/slices/productsSlice';
import providerReducer from 'store/slices/providerSlice';
import signUpReducer from 'store/slices/signUpSlice';
import userReducer from 'store/slices/userSlice';
import postReducer from 'store/slices/postSlice';

const reducers = combineReducers({
  product: productsReducer,
  auth: authReducer,
  cart: cartReducer,
  user: userReducer,
  category: categoryReducer,
  checkout: checkoutReducer,
  modal: modalReducer,
  compare: compareReducer,
  UserAddress: UserAddressReducer,
  forgetPassword: forgetPasswordReducer,
  currency: currencyReducer,
  signUp: signUpReducer,
  provider: providerReducer,
  checkoutSummary: checkoutSummaryReducer,
  posts:postReducer
});

const persistConfig = {
  key: 'root',
  storage,
  whiteList: ['cart', 'auth', 'checkout'],
};

const persistedReducer = persistReducer(persistConfig, reducers);

export const store = configureStore({
  reducer: { persistedReducer },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
