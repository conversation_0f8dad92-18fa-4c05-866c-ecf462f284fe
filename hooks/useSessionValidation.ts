import { useEffect, useCallback, useRef } from 'react';
import { useAppSelector, useAppDispatch } from 'store/hooks';
import { updateSessionValidation } from 'store/slices/authSlice';
import { sessionManager } from 'utils/sessionManager';

interface UseSessionValidationOptions {
  /**
   * Interval in milliseconds to check session validity
   * Default: 5 minutes (300000ms)
   */
  validationInterval?: number;
  
  /**
   * Whether to validate session immediately on mount
   * Default: true
   */
  validateOnMount?: boolean;
  
  /**
   * Whether to validate session when window gains focus
   * Default: true
   */
  validateOnFocus?: boolean;
  
  /**
   * Whether the hook is enabled
   * Default: true
   */
  enabled?: boolean;
}

export const useSessionValidation = (options: UseSessionValidationOptions = {}) => {
  const {
    validationInterval = 5 * 60 * 1000, // 5 minutes
    validateOnMount = true,
    validateOnFocus = true,
    enabled = true
  } = options;

  const dispatch = useAppDispatch();
  const token = useAppSelector(state => state.persistedReducer.auth.access_token);
  const isSessionValid = useAppSelector(state => state.persistedReducer.auth.isSessionValid);
  const lastValidationTime = useAppSelector(state => state.persistedReducer.auth.lastValidationTime);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isValidatingRef = useRef(false);

  const validateSession = useCallback(async () => {
    if (!enabled || !token || isValidatingRef.current) {
      return;
    }

    isValidatingRef.current = true;

    try {
      console.log('🔍 useSessionValidation: Validating session...');
      
      const isValid = await sessionManager.validateSession(token);
      
      dispatch(updateSessionValidation({
        isValid,
        timestamp: Date.now()
      }));

      if (!isValid) {
        console.log('🚨 useSessionValidation: Session is invalid, triggering invalidation');
        await sessionManager.handleSingleSessionInvalidation();
      } else {
        console.log('✅ useSessionValidation: Session is valid');
      }
    } catch (error) {
      console.error('💥 useSessionValidation: Error validating session:', error);
      
      dispatch(updateSessionValidation({
        isValid: false,
        timestamp: Date.now()
      }));
      
      await sessionManager.handleSingleSessionInvalidation();
    } finally {
      isValidatingRef.current = false;
    }
  }, [enabled, token, dispatch]);

  const shouldValidate = useCallback(() => {
    if (!enabled || !token) {
      return false;
    }

    // If we've never validated, we should validate
    if (!lastValidationTime) {
      return true;
    }

    // If it's been longer than the validation interval, we should validate
    const timeSinceLastValidation = Date.now() - lastValidationTime;
    return timeSinceLastValidation >= validationInterval;
  }, [enabled, token, lastValidationTime, validationInterval]);

  // Set up periodic validation
  useEffect(() => {
    if (!enabled || !token) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Clear existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Set up new interval
    intervalRef.current = setInterval(() => {
      if (shouldValidate()) {
        validateSession();
      }
    }, validationInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [enabled, token, validationInterval, shouldValidate, validateSession]);

  // Validate on mount
  useEffect(() => {
    if (validateOnMount && shouldValidate()) {
      validateSession();
    }
  }, [validateOnMount, shouldValidate, validateSession]);

  // Validate when window gains focus
  useEffect(() => {
    if (!validateOnFocus || !enabled) {
      return;
    }

    const handleFocus = () => {
      if (shouldValidate()) {
        validateSession();
      }
    };

    window.addEventListener('focus', handleFocus);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [validateOnFocus, enabled, shouldValidate, validateSession]);

  // Manual validation function
  const manualValidate = useCallback(() => {
    validateSession();
  }, [validateSession]);

  return {
    isSessionValid,
    lastValidationTime,
    validateSession: manualValidate,
    isValidating: isValidatingRef.current
  };
};
