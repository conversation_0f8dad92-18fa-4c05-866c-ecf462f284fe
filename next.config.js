const path = require('path');
const nextTranslate = require('next-translate');

const nextConfig = {
  reactStrictMode: true,
  // async redirects() {
  //   return [
  //     {
  //       source: '/market',
  //       destination: '/market',
  //       permanent: true,
  //     },
  //   ];
  // },
  // async rewrites() {
  //   return [
  //     {
  //       source: '/.well-known/apple-app-site-association',
  //       destination: '/api/apple-app-site-association',
  //     },
  //   ];
  // },
  images: {
    loader: 'custom',
    loaderFile: './image/loader.js',
  },
  // webpack: (config, options) => {
  //   config.module.rules.push({
  //     test: /\.(ts|tsx)$/,
  //     //include: [path.resolve(__dirname, '../atomic-components')],
  //     use: [
  //       {
  //         loader: 'babel-loader',
  //         options: {
  //           presets: ['next/babel'],
  //           // plugins: ['react-docgen']
  //         },
  //       },
  //     ],
  //   });

  //   return config;
  // },
};

const withTM = require('next-transpile-modules')([]);
module.exports = withTM(nextTranslate(nextConfig));
