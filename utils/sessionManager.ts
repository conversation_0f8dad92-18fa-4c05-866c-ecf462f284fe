import { signOut } from "next-auth/react";
import { toast } from "react-toastify";
import { clearAuthState } from "store/slices/authSlice";
import { resetCart } from "store/slices/cartSlice";
import { deleteCheckoutInfo } from "store/slices/checkoutSlice";
import { resetCompare } from "store/slices/compareSlice";
import { resetAddress } from "store/slices/customerAddressSlice";
import { resetWishilist } from "store/slices/productsSlice";
import { storeProvider } from "store/slices/providerSlice";
import { resetUserDetails } from "store/slices/userSlice";
import { AppDispatch } from "store/store";

export interface SessionInvalidationOptions {
  showToast?: boolean;
  redirectTo?: string;
  reason?: 'logout' | 'session_expired' | 'unauthorized' | 'single_session_policy';
  silent?: boolean;
}

export class SessionManager {
  private static instance: SessionManager;
  private dispatch: AppDispatch | null = null;
  private isInvalidating = false;

  private constructor() {}

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  public setDispatch(dispatch: AppDispatch) {
    this.dispatch = dispatch;
  }

  /**
   * Comprehensive session invalidation that clears all authentication state
   */
  public async invalidateSession(options: SessionInvalidationOptions = {}): Promise<void> {
    // Prevent multiple simultaneous invalidations
    if (this.isInvalidating) {
      console.log('🚨 SessionManager: Already invalidating, skipping...');
      return;
    }

    this.isInvalidating = true;

    try {
      const {
        showToast = true,
        redirectTo = '/account/sign-in',
        reason = 'session_expired',
        silent = false
      } = options;

      console.log('🚨 SessionManager: Invalidating session', { reason, silent });

      if (!this.dispatch) {
        console.error('SessionManager: Dispatch not set');
        this.isInvalidating = false;
        return;
      }

      // Clear all Redux state first
      this.dispatch(clearAuthState());
      this.dispatch(resetAddress());
      this.dispatch(resetUserDetails());
      this.dispatch(resetWishilist());
      this.dispatch(resetCart());
      this.dispatch(resetCompare());
      this.dispatch(deleteCheckoutInfo());
      this.dispatch(storeProvider('none'));

      // Clear localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('persist:root');
      }

      // Clear NextAuth session
      await signOut({ redirect: false });

      // Show appropriate toast message
      if (showToast && !silent) {
        const messages = {
          logout: 'Logged out successfully!',
          session_expired: 'Your session has expired. Please log in again.',
          unauthorized: 'Authentication failed. Please log in again.',
          single_session_policy: 'You have been logged out because you signed in from another device.'
        };

        const toastType = reason === 'logout' ? 'success' : 'error';
        const message = messages[reason] || messages.session_expired;

        if (toastType === 'success') {
          toast.success(message, { containerId: 'bottom-right' });
        } else {
          toast.error(message, { containerId: 'bottom-right' });
        }
      }

      // Use a small delay to ensure state updates are processed
      await new Promise(resolve => setTimeout(resolve, 100));

      // Redirect to appropriate page
      if (typeof window !== 'undefined' && redirectTo) {
        const currentPath = window.location.pathname;

        // Don't redirect if already on the target page
        if (currentPath !== redirectTo) {
          console.log('🚨 SessionManager: Redirecting to', redirectTo);
          // Use router.push instead of window.location.href for better UX
          const { default: Router } = await import('next/router');
          Router.push(redirectTo);
        }
      }

    } catch (error) {
      console.error('SessionManager: Error during session invalidation:', error);
    } finally {
      // Reset the flag after a small delay to ensure everything is processed
      setTimeout(() => {
        this.isInvalidating = false;
      }, 500);
    }
  }

  /**
   * Check if the current session is valid by making a test API call
   */
  public async validateSession(token: string): Promise<boolean> {
    if (!token) {
      return false;
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_PREFIX_REST}/user`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.status === 200;
    } catch (error) {
      console.error('SessionManager: Session validation failed:', error);
      return false;
    }
  }

  /**
   * Handle 401 responses from API calls
   */
  public async handle401Response(_error?: any): Promise<void> {
    console.log('🚨 SessionManager: Handling 401 response');
    
    // Check if we're already on a public page to avoid unnecessary redirects
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname;
      const publicPaths = ['/account/sign-in', '/account/sign-up', '/product', '/', '/pricing'];
      
      if (publicPaths.some(path => currentPath.includes(path))) {
        console.log('SessionManager: Already on public page, skipping invalidation');
        return;
      }
    }

    await this.invalidateSession({
      reason: 'unauthorized',
      showToast: true,
      redirectTo: '/account/sign-in'
    });
  }

  /**
   * Handle session invalidation due to single-session policy
   */
  public async handleSingleSessionInvalidation(): Promise<void> {
    console.log('🚨 SessionManager: Handling single-session invalidation');
    
    await this.invalidateSession({
      reason: 'single_session_policy',
      showToast: true,
      redirectTo: '/account/sign-in'
    });
  }

  /**
   * Manual logout initiated by user
   */
  public async logout(): Promise<void> {
    console.log('🚨 SessionManager: Manual logout');
    
    await this.invalidateSession({
      reason: 'logout',
      showToast: true,
      redirectTo: '/account/sign-in'
    });
  }
}

// Export singleton instance
export const sessionManager = SessionManager.getInstance();
