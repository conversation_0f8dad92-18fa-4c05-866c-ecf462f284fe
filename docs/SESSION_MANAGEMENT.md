# Session Management System

This document describes the enhanced session management system implemented to handle session invalidation properly, especially for single-session policy scenarios.

## Overview

The session management system provides comprehensive handling of authentication state, session validation, and proper cleanup when sessions are invalidated due to various reasons including:

- Session expiration
- Single-session policy enforcement (user logs in from another device)
- Authentication failures
- Manual logout

## Architecture

### Core Components

1. **SessionManager** (`utils/sessionManager.ts`)
   - Singleton class that handles all session invalidation logic
   - Provides centralized session cleanup and state management
   - Handles different invalidation reasons with appropriate user feedback

2. **Enhanced Auth Slice** (`store/slices/authSlice.ts`)
   - Extended to include session validation state
   - Tracks last validation time
   - Provides actions for session state management

3. **Session Validation Hook** (`hooks/useSessionValidation.ts`)
   - React hook for periodic session validation
   - Configurable validation intervals
   - Validates on window focus and mount

4. **Session Context** (`contexts/SessionContext.tsx`)
   - Provides session management functions across the app
   - Integrates with session validation hook
   - Offers both context-based and standalone usage

5. **Enhanced Axios Interceptors** (`pages/_app.tsx`)
   - Request interceptor automatically adds JWT tokens
   - Response interceptor handles 401 errors with proper cleanup

## Key Features

### Automatic Session Validation

The system automatically validates sessions:
- Every 5 minutes (configurable)
- When the browser window gains focus
- On component mount for protected routes

### Comprehensive State Cleanup

When a session is invalidated, the system:
- Clears Redux state (auth, cart, wishlist, addresses, etc.)
- Removes persisted localStorage data
- Signs out from NextAuth
- Shows appropriate user feedback
- Redirects to sign-in page

### Multiple Invalidation Scenarios

The system handles different invalidation reasons:
- `logout`: User-initiated logout
- `session_expired`: Session has expired
- `unauthorized`: 401 response from API
- `single_session_policy`: User logged in from another device

### Enhanced Error Handling

- 401 responses trigger automatic session cleanup
- API calls automatically include authentication headers
- Graceful handling of network errors during validation

## Usage

### Using Session Manager Directly

```typescript
import { sessionManager } from 'utils/sessionManager';

// Manual logout
await sessionManager.logout();

// Handle single-session invalidation
await sessionManager.handleSingleSessionInvalidation();

// Custom invalidation
await sessionManager.invalidateSession({
  reason: 'session_expired',
  showToast: true,
  redirectTo: '/account/sign-in'
});
```

### Using Session Context

```typescript
import { useSessionManager } from 'contexts/SessionContext';

const MyComponent = () => {
  const { logout, handleSingleSessionInvalidation } = useSessionManager();
  
  const handleLogout = async () => {
    await logout();
  };
  
  return <button onClick={handleLogout}>Logout</button>;
};
```

### Using Session Validation Hook

```typescript
import { useSessionValidation } from 'hooks/useSessionValidation';

const MyComponent = () => {
  const { isSessionValid, validateSession } = useSessionValidation({
    validationInterval: 5 * 60 * 1000, // 5 minutes
    validateOnMount: true,
    validateOnFocus: true
  });
  
  return (
    <div>
      Session Status: {isSessionValid ? 'Valid' : 'Invalid'}
    </div>
  );
};
```

## Configuration

### Environment Variables

The system uses these environment variables:
- `NEXT_PUBLIC_API_PREFIX_REST`: Backend API base URL
- `NODE_ENV`: Controls development features like session status indicator

### Validation Intervals

Default validation interval is 5 minutes, but can be configured:

```typescript
const { isSessionValid } = useSessionValidation({
  validationInterval: 2 * 60 * 1000, // 2 minutes
});
```

## Development Tools

### Session Status Indicator

A visual indicator shows session status in development mode:
- Green: Valid session
- Red: Invalid session
- Yellow: Unknown status
- Gray: No token

### Test Page

Visit `/session-test` to access the session management test page with:
- Current session status display
- Test buttons for various scenarios
- Real-time test results

## Integration Points

### Protected Routes

The `withAuth` HOC automatically:
- Validates session state
- Handles invalid sessions
- Redirects unauthenticated users

### API Calls

All API calls automatically:
- Include JWT tokens in headers
- Handle 401 responses with session cleanup
- Provide consistent error handling

### User Interface

Components can access session state through:
- Redux selectors for auth state
- Session context for management functions
- Session validation hook for real-time status

## Best Practices

1. **Use Session Manager for Logout**: Always use `sessionManager.logout()` instead of manual cleanup
2. **Handle 401 Responses**: Let the Axios interceptor handle 401s automatically
3. **Check Session Validity**: Use `isSessionValid` state for UI decisions
4. **Provide User Feedback**: The system automatically shows appropriate toast messages
5. **Test Scenarios**: Use the test page to verify session handling works correctly

## Troubleshooting

### Common Issues

1. **Session not invalidating**: Check if SessionValidationProvider is properly wrapped around the app
2. **Multiple redirects**: Ensure only one session invalidation is triggered at a time
3. **State not clearing**: Verify all Redux slices have proper reset actions
4. **API calls failing**: Check if JWT tokens are being included in headers

### Debug Information

Enable debug logging by checking browser console for messages prefixed with:
- `🔍` Session validation
- `🚨` Session invalidation
- `✅` Successful operations
- `❌` Errors

## Future Enhancements

Potential improvements:
- WebSocket integration for real-time session invalidation
- Refresh token handling
- Session persistence across browser tabs
- Advanced retry logic for failed validations
