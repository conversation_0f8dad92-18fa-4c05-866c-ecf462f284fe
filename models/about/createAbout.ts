import { SuccessResponse } from 'models/common';
import { About } from './about';

/**
 * API Path: /api/about
 * method: POST
 * Body: CreateAboutRequestBody
 * response: CreateAboutResponse
 */
export interface CreateAboutRequestBody {
  privacyPolicy: string;
  termsOfUse: string;
  aboutUs: string;
}
export interface CreateAboutSuccessResponse extends SuccessResponse {
  data: About;
}
export type CreateAboutResponse = CreateAboutSuccessResponse;
