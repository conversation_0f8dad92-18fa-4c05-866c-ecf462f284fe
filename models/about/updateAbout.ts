import { SuccessResponse } from 'models/common';
import { About } from './about';

/**
 * API Path: /api/about
 * method: PATCH
 * Body: UpdateAboutRequestBody
 * response: UpdateAboutResponse
 */

export interface UpdateAboutRequestBody {
  privacyPolicy?: string;
  termsOfUse?: string;
  aboutUs?: string;
}
export interface UpdateAboutSuccessResponse extends SuccessResponse {
  data: About;
}
export type UpdateAboutResponse = UpdateAboutSuccessResponse;
