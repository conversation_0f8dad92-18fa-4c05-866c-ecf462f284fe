export interface UserProductInfo {
  name: string;
  shortDescription?: string;
  fullDescription?: string;
  sku: string;
  price: number;
  oldPrice: number;
  showOnHomePage?: boolean;
  includeInTopMenu?: boolean;
  allowToSelectPageSize?: boolean;
  published?: boolean;
  displayOrder?: number;
  isFeatured?: boolean;
  publishDate?: Date;
  size?: string[];
  color?: string[];
  stock?: number;
  length?: number;
  height?: number;
  width?: number;
  weight?: number;
  weightUnit?: string;
}

export interface UserProductMeta {
  keywords?: string[];
  title?: string;
  description?: string;
  friendlyPageName?: string;
}

export interface UserProductPhoto {
  url?: string;
  id?: string;
  title?: string;
  alt?: string;
  displayOrder?: number;
}

export interface UserProductManufacturer {
  id?: string;
  name?: string;
}

export interface UserProductCategory {
  id: string;
  name: string;
  displayOrder?: number;
}

export interface UserProduct {
  id?: string;
  info: UserProductInfo;
  meta: UserProductMeta;
  tags?: string[];
  photos?: UserProductPhoto[];
  brands?: string[];
  manufacturer?: UserProductManufacturer;
  categories: UserProductCategory[];
  avgRating: number;
}
