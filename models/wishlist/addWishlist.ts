import { SuccessResponse } from '../common/index';
import { Wishlist } from './wishlist';

/**
 * API Path: /wishlist
 * method: POST
 * body: AddToWishlistRequest
 * response: AddToWishlistResponse
 */

export interface AddToWishlistRequest {
  productId: string;
  quantity: number;
}

export interface AddToWishlistSuccessResponse extends SuccessResponse {
  data: Wishlist;
}

export const enum AddToWishlistErrorMessage {
  CAN_NOT_CREATE_WISHLIST = 'Can not create wishlist',
  CAN_NOT_ADD_ITEM_TO_THE_WISHLIST = 'Can not add item to the wishlist',
  CAN_NOT_INCREMENT_WISHLIST_ITEM = 'Can not increment wishlist item',
}
