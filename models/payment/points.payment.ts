import { PaymentMethodEnum } from './enums.payment.interface';

export interface IPointsPaymentRes {
  success: boolean;
  message: string;
}

export interface IAmountPerPoint {
  id: string;
  amount: number;
}

export interface IPaymentMethodList {
  id: string;
  list: PaymentMethodEnum[];
}

export interface IAmountPerPointSuccessRes {
  data: IAmountPerPoint;
}

export interface IPaymentMethodListSuccessRes {
  data: IPaymentMethodList;
}
