import { SuccessResponse } from '../common/index';

/**
 * API Path: /user-auth/apple/sign-in
 * method: POST
 * body: AppleSignInRequest
 * response: AppleSignInResponse
 */

export interface AppleSignInRequest {
  idToken: string;
  name?: string;
  fcmToken?: string;
}

export interface AppleSignInSuccessResponse extends SuccessResponse {
  data: {
    token: string;
  };
}

export const enum AppleSignInErrorMessages {
  NO_APPLE_USER_FOUND = 'No apple user found',
  SERVER_ERROR = 'Server error',
}
