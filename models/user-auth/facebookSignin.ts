import { SuccessResponse } from '../common/index';

/**
 * API Path: /user-auth/facebook/sign-in
 * method: POST
 * body: FacebookSignInRequest
 * response: FacebookSignInResponse
 */

export interface FacebookSignInRequest {
  accessToken: string;
  fcmToken?: string;
}

export interface FacebookSignInSuccessResponse extends SuccessResponse {
  data: {
    token: string;
  };
}

export const enum FacebookSignInErrorMessages {
  NO_FACEBOOK_USER_FOUND = 'No facebook user found',
  SERVER_ERROR = 'Server error',
}
