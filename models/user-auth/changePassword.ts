import { SuccessResponse } from '../common/index';

/**
 * API Path: /user/change-password
 * method: PATCH
 * body: UserChangePasswordRequest
 * response: UserChangePasswordResponse
 */

export interface UserChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export const enum UserChangePasswordSuccessMessage {
  PASSWORD_CHANGED_SUCCESSFUL = 'Password changed Successfully',
}

export interface UserChangePasswordSuccessResponse extends SuccessResponse {
  data: {
    message?: UserChangePasswordSuccessMessage;
  };
}

export const enum UserChangePasswordErrorMessages {
  INVALID_USER = 'Invalid user',
  CURRENT_PASSWORD_IS_INCORRECT = 'Current password is incorrect',
  PASSWORD_CHANGED_FAILED = 'Failed to change password',
}
