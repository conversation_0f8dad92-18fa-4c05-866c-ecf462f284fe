import { SuccessResponse } from '../common/index';

/**
 * API Path: /user-auth/sign-in
 * method: POST
 * body: UserSignInRequest
 * response: UserSignInResponse
 */

export interface UserSignInRequest {
  phone?: string;
  email?: string;
  password: string;
  fcmToken?: string;
}

export interface UserSignInSuccessResponse extends SuccessResponse {
  data: {
    token: string;
  };
}

export const enum UserSignInErrorMessages {
  INVALID_CREDENTIALS = 'Invalid credentials',
}
