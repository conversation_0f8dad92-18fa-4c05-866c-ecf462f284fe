export interface CartProduct {
  id?: string;
  info: CartProductInfo;
  meta: CartProductMeta;
  photos?: CartProductPhoto[];
}

export interface CartProductInfo {
  name: string;
  shortDescription?: string;
  fullDescription?: string;
  sku: string;
  price: number;
  oldPrice: number;
  size?: string[];
  color?: string[];
  showOnHomePage?: boolean;
  includeInTopMenu?: boolean;
  allowToSelectPageSize?: boolean;
  published?: boolean;
  displayOrder?: number;
  isFeatured?: boolean;
  publishDate?: Date;
  stock?: number;
}

export interface CartProductPhoto {
  url?: string;
  title?: string;
  alt?: string;
  displayOrder?: number;
}

export interface CartProductMeta {
  friendlyPageName: string;
}
