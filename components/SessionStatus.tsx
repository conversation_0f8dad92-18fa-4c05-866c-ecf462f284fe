import React from 'react';
import { useAppSelector } from 'store/hooks';

interface SessionStatusProps {
  /**
   * Whether to show the component (useful for development)
   */
  show?: boolean;
  
  /**
   * Position of the status indicator
   */
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

const SessionStatus: React.FC<SessionStatusProps> = ({ 
  show = process.env.NODE_ENV === 'development',
  position = 'bottom-right'
}) => {
  const token = useAppSelector(state => state.persistedReducer.auth.access_token);
  const isSessionValid = useAppSelector(state => state.persistedReducer.auth.isSessionValid);
  const lastValidationTime = useAppSelector(state => state.persistedReducer.auth.lastValidationTime);

  if (!show) {
    return null;
  }

  const getStatusColor = () => {
    if (!token) return 'bg-gray-500';
    if (isSessionValid === false) return 'bg-red-500';
    if (isSessionValid === true) return 'bg-green-500';
    return 'bg-yellow-500'; // Unknown status
  };

  const getStatusText = () => {
    if (!token) return 'No Token';
    if (isSessionValid === false) return 'Invalid Session';
    if (isSessionValid === true) return 'Valid Session';
    return 'Unknown Status';
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
      default:
        return 'bottom-4 right-4';
    }
  };

  const formatTime = (timestamp: number | null) => {
    if (!timestamp) return 'Never';
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div 
      className={`fixed ${getPositionClasses()} z-50 p-3 rounded-lg shadow-lg text-white text-xs max-w-xs`}
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.8)' }}
    >
      <div className="flex items-center space-x-2 mb-2">
        <div className={`w-3 h-3 rounded-full ${getStatusColor()}`}></div>
        <span className="font-semibold">{getStatusText()}</span>
      </div>
      
      <div className="space-y-1 text-xs opacity-80">
        <div>Token: {token ? '✓' : '✗'}</div>
        <div>Valid: {isSessionValid === null ? '?' : isSessionValid ? '✓' : '✗'}</div>
        <div>Last Check: {formatTime(lastValidationTime)}</div>
      </div>
    </div>
  );
};

export default SessionStatus;
